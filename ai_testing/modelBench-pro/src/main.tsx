import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import ErrorBoundary from './components/common/ErrorBoundary';
import './styles/globals.css';

/**
 * Main application entry point
 * Initializes React application and renders the root component
 */
function initializeApp(): void {
  const container = document.getElementById('root');
  
  if (!container) {
    throw new Error('Failed to find the root element. Make sure index.html contains a div with id="root"');
  }

  // Create React root
  const root = createRoot(container);

  // Render the application
  root.render(
    <React.StrictMode>
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    </React.StrictMode>
  );

  // Log successful initialization in development
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 ModelBench Pro initialized successfully');
    console.log('📊 Environment:', process.env.NODE_ENV);
    console.log('⚡ React version:', React.version);
    console.log('🔧 Electron API available:', typeof (window as any).electronAPI !== 'undefined');
  }
  
  // Mark app as loaded for CSS transitions
  setTimeout(() => {
    document.body.classList.add('app-loaded');
  }, 100);
}

/**
 * Error handler for uncaught errors during initialization
 */
function handleInitializationError(error: Error): void {
  console.error('❌ Failed to initialize ModelBench Pro:', error);
  
  // Show user-friendly error message
  const container = document.getElementById('root');
  if (container) {
    container.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        text-align: center;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      ">
        <h1 style="margin-bottom: 1rem;">Initialization Error</h1>
        <p style="margin-bottom: 1rem; opacity: 0.8;">
          ModelBench Pro failed to start. Please check the console for details.
        </p>
        <button 
          onclick="window.location.reload()" 
          style="
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 0.5rem;
            color: white;
            cursor: pointer;
            font-size: 1rem;
          "
        >
          Reload Application
        </button>
      </div>
    `;
  }
}

// Initialize the application with error handling
try {
  initializeApp();
} catch (error) {
  handleInitializationError(error as Error);
}

// Handle hot module replacement in development
if (process.env.NODE_ENV === 'development' && (module as any).hot) {
  (module as any).hot.accept('./App', () => {
    console.log('🔄 Hot reloading App component...');
    initializeApp();
  });
}