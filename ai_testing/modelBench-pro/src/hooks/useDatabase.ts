import { useCallback, useEffect, useState } from 'react';
import { electronAPI } from '../types/electron';
import {
  Model,
  Benchmark,
  TestRun,
  Result,
  Configuration,
  CreateModelInput,
  CreateBenchmarkInput,
  CreateTestRunInput,
  CreateResultInput,
  CreateConfigurationInput,
  UpdateModelInput,
  UpdateTestRunInput,
  ModelQuery,
  BenchmarkQuery,
  DatabaseResult,
  PaginatedResult,
  DatabaseStats
} from '../types/database';

// Custom hook for database operations
export function useDatabase() {
  const [isConnected, setIsConnected] = useState(false);
  const [stats, setStats] = useState<DatabaseStats | null>(null);

  // Check database connection on mount
  useEffect(() => {
    const checkConnection = async () => {
      try {
        // Check if electronAPI is available
        if (typeof window !== 'undefined' && (window as any).electronAPI) {
          const result = await electronAPI.getDatabaseStats();
          if (result.success) {
            setIsConnected(true);
            setStats(result.data!);
          } else {
            setIsConnected(false);
            console.error('Database connection failed:', result.error);
          }
        } else {
          // Fallback for browser mode - set mock data
          console.warn('Electron API not available - running in browser mode');
          setIsConnected(false);
          setStats({
            models: 0,
            benchmarks: 0,
            testRuns: 0,
            results: 0,
            configurations: 0
          });
        }
      } catch (error) {
        setIsConnected(false);
        console.error('Database connection error:', error);
        // Set fallback stats
        setStats({
          models: 0,
          benchmarks: 0,
          testRuns: 0,
          results: 0,
          configurations: 0
        });
      }
    };

    checkConnection();
  }, []);

  // Model operations
  const createModel = useCallback(async (input: CreateModelInput): Promise<DatabaseResult<Model>> => {
    if (typeof window !== 'undefined' && (window as any).electronAPI) {
      return electronAPI.createModel(input);
    }
    return { success: false, error: 'Electron API not available' };
  }, []);

  const getModels = useCallback(async (
    query?: ModelQuery,
    page?: number,
    pageSize?: number
  ): Promise<DatabaseResult<PaginatedResult<Model>>> => {
    if (typeof window !== 'undefined' && (window as any).electronAPI) {
      return electronAPI.getModels(query, page, pageSize);
    }
    return {
      success: true,
      data: {
        data: [],
        total: 0,
        page: page || 1,
        pageSize: pageSize || 20,
        totalPages: 0
      }
    };
  }, []);

  const getModelById = useCallback(async (id: number): Promise<DatabaseResult<Model>> => {
    return electronAPI.getModelById(id);
  }, []);

  const updateModel = useCallback(async (input: UpdateModelInput): Promise<DatabaseResult<Model>> => {
    return electronAPI.updateModel(input);
  }, []);

  const deleteModel = useCallback(async (id: number): Promise<DatabaseResult<void>> => {
    return electronAPI.deleteModel(id);
  }, []);

  // Benchmark operations
  const createBenchmark = useCallback(async (input: CreateBenchmarkInput): Promise<DatabaseResult<Benchmark>> => {
    return electronAPI.createBenchmark(input);
  }, []);

  const getBenchmarks = useCallback(async (
    query?: BenchmarkQuery,
    page?: number,
    pageSize?: number
  ): Promise<DatabaseResult<PaginatedResult<Benchmark>>> => {
    return electronAPI.getBenchmarks(query, page, pageSize);
  }, []);

  const getBenchmarkById = useCallback(async (id: number): Promise<DatabaseResult<Benchmark>> => {
    return electronAPI.getBenchmarkById(id);
  }, []);

  // Test run operations
  const createTestRun = useCallback(async (input: CreateTestRunInput): Promise<DatabaseResult<TestRun>> => {
    return electronAPI.createTestRun(input);
  }, []);

  const getTestRunById = useCallback(async (id: number): Promise<DatabaseResult<TestRun>> => {
    return electronAPI.getTestRunById(id);
  }, []);

  const updateTestRun = useCallback(async (input: UpdateTestRunInput): Promise<DatabaseResult<TestRun>> => {
    return electronAPI.updateTestRun(input);
  }, []);

  // Result operations
  const createResult = useCallback(async (input: CreateResultInput): Promise<DatabaseResult<Result>> => {
    return electronAPI.createResult(input);
  }, []);

  const getResultById = useCallback(async (id: number): Promise<DatabaseResult<Result>> => {
    return electronAPI.getResultById(id);
  }, []);

  // Configuration operations
  const createConfiguration = useCallback(async (input: CreateConfigurationInput): Promise<DatabaseResult<Configuration>> => {
    return electronAPI.createConfiguration(input);
  }, []);

  const getConfigurationById = useCallback(async (id: number): Promise<DatabaseResult<Configuration>> => {
    return electronAPI.getConfigurationById(id);
  }, []);

  const getDefaultConfiguration = useCallback(async (): Promise<DatabaseResult<Configuration>> => {
    return electronAPI.getDefaultConfiguration();
  }, []);

  // Utility operations
  const getDatabaseStats = useCallback(async (): Promise<DatabaseResult<DatabaseStats>> => {
    const result = await electronAPI.getDatabaseStats();
    if (result.success) {
      setStats(result.data!);
    }
    return result;
  }, []);

  const backupDatabase = useCallback(async (backupPath: string): Promise<DatabaseResult<void>> => {
    return electronAPI.backupDatabase(backupPath);
  }, []);

  const executeTransaction = useCallback(async (operations: any[]): Promise<DatabaseResult<any[]>> => {
    return electronAPI.executeTransaction(operations);
  }, []);

  // Refresh stats
  const refreshStats = useCallback(async () => {
    await getDatabaseStats();
  }, [getDatabaseStats]);

  return {
    // Connection status
    isConnected,
    stats,
    refreshStats,

    // Model operations
    createModel,
    getModels,
    getModelById,
    updateModel,
    deleteModel,

    // Benchmark operations
    createBenchmark,
    getBenchmarks,
    getBenchmarkById,

    // Test run operations
    createTestRun,
    getTestRunById,
    updateTestRun,

    // Result operations
    createResult,
    getResultById,

    // Configuration operations
    createConfiguration,
    getConfigurationById,
    getDefaultConfiguration,

    // Utility operations
    getDatabaseStats,
    backupDatabase,
    executeTransaction
  };
}

// Hook for specific model operations with state management
export function useModels() {
  const [models, setModels] = useState<Model[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });

  const { getModels, createModel, updateModel, deleteModel } = useDatabase();

  const loadModels = useCallback(async (query?: ModelQuery, page = 1, pageSize = 20) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await getModels(query, page, pageSize);
      if (result.success && result.data) {
        setModels(result.data.data);
        setPagination({
          page: result.data.page,
          pageSize: result.data.pageSize,
          total: result.data.total,
          totalPages: result.data.totalPages
        });
      } else {
        setError(result.error || 'Failed to load models');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [getModels]);

  const addModel = useCallback(async (input: CreateModelInput) => {
    const result = await createModel(input);
    if (result.success) {
      // Refresh the list
      await loadModels();
    }
    return result;
  }, [createModel, loadModels]);

  const editModel = useCallback(async (input: UpdateModelInput) => {
    const result = await updateModel(input);
    if (result.success) {
      // Refresh the list
      await loadModels();
    }
    return result;
  }, [updateModel, loadModels]);

  const removeModel = useCallback(async (id: number) => {
    const result = await deleteModel(id);
    if (result.success) {
      // Refresh the list
      await loadModels();
    }
    return result;
  }, [deleteModel, loadModels]);

  return {
    models,
    loading,
    error,
    pagination,
    loadModels,
    addModel,
    editModel,
    removeModel
  };
}

// Hook for specific benchmark operations with state management
export function useBenchmarks() {
  const [benchmarks, setBenchmarks] = useState<Benchmark[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });

  const { getBenchmarks, createBenchmark } = useDatabase();

  const loadBenchmarks = useCallback(async (query?: BenchmarkQuery, page = 1, pageSize = 20) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await getBenchmarks(query, page, pageSize);
      if (result.success && result.data) {
        setBenchmarks(result.data.data);
        setPagination({
          page: result.data.page,
          pageSize: result.data.pageSize,
          total: result.data.total,
          totalPages: result.data.totalPages
        });
      } else {
        setError(result.error || 'Failed to load benchmarks');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [getBenchmarks]);

  const addBenchmark = useCallback(async (input: CreateBenchmarkInput) => {
    const result = await createBenchmark(input);
    if (result.success) {
      // Refresh the list
      await loadBenchmarks();
    }
    return result;
  }, [createBenchmark, loadBenchmarks]);

  return {
    benchmarks,
    loading,
    error,
    pagination,
    loadBenchmarks,
    addBenchmark
  };
}