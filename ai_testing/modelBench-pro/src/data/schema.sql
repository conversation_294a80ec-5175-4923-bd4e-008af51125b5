-- ModelBench Pro Database Schema
-- SQLite database schema for AI model benchmarking application

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Models table - stores AI model information
CREATE TABLE IF NOT EXISTS models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    provider TEXT NOT NULL,
    type TEXT NOT NULL,
    parameters TEXT NOT NULL DEFAULT '{}', -- JSON string of model parameters
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Benchmarks table - stores benchmark test definitions
CREATE TABLE IF NOT EXISTS benchmarks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    metrics TEXT NOT NULL DEFAULT '{}', -- J<PERSON><PERSON> string of metric definitions
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Test runs table - stores individual benchmark execution records
CREATE TABLE IF NOT EXISTS test_runs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_id INTEGER NOT NULL,
    benchmark_id INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME NULL,
    results TEXT NULL, -- JSON string of aggregated results
    FOREIGN KEY (model_id) REFERENCES models(id) ON DELETE CASCADE,
    FOREIGN KEY (benchmark_id) REFERENCES benchmarks(id) ON DELETE CASCADE
);

-- Results table - stores individual metric results from test runs
CREATE TABLE IF NOT EXISTS results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    test_run_id INTEGER NOT NULL,
    metric_name TEXT NOT NULL,
    value REAL NOT NULL,
    unit TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (test_run_id) REFERENCES test_runs(id) ON DELETE CASCADE
);

-- Configurations table - stores application and benchmark configurations
CREATE TABLE IF NOT EXISTS configurations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    settings TEXT NOT NULL DEFAULT '{}', -- JSON string of configuration settings
    is_default BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Database metadata table - for schema versioning and migrations
CREATE TABLE IF NOT EXISTS db_metadata (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_models_provider ON models(provider);
CREATE INDEX IF NOT EXISTS idx_models_type ON models(type);
CREATE INDEX IF NOT EXISTS idx_models_created_at ON models(created_at);

CREATE INDEX IF NOT EXISTS idx_benchmarks_category ON benchmarks(category);
CREATE INDEX IF NOT EXISTS idx_benchmarks_created_at ON benchmarks(created_at);

CREATE INDEX IF NOT EXISTS idx_test_runs_model_id ON test_runs(model_id);
CREATE INDEX IF NOT EXISTS idx_test_runs_benchmark_id ON test_runs(benchmark_id);
CREATE INDEX IF NOT EXISTS idx_test_runs_status ON test_runs(status);
CREATE INDEX IF NOT EXISTS idx_test_runs_started_at ON test_runs(started_at);
CREATE INDEX IF NOT EXISTS idx_test_runs_completed_at ON test_runs(completed_at);

CREATE INDEX IF NOT EXISTS idx_results_test_run_id ON results(test_run_id);
CREATE INDEX IF NOT EXISTS idx_results_metric_name ON results(metric_name);
CREATE INDEX IF NOT EXISTS idx_results_timestamp ON results(timestamp);

CREATE INDEX IF NOT EXISTS idx_configurations_is_default ON configurations(is_default);
CREATE INDEX IF NOT EXISTS idx_configurations_created_at ON configurations(created_at);

-- Triggers for automatic updated_at timestamp updates
CREATE TRIGGER IF NOT EXISTS update_models_timestamp 
    AFTER UPDATE ON models
    FOR EACH ROW
    BEGIN
        UPDATE models SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Insert initial database version
INSERT OR IGNORE INTO db_metadata (key, value) VALUES ('schema_version', '1.0.0');
INSERT OR IGNORE INTO db_metadata (key, value) VALUES ('created_at', CURRENT_TIMESTAMP);

-- Insert default configuration
INSERT OR IGNORE INTO configurations (name, settings, is_default) 
VALUES ('Default Configuration', '{"timeout": 300, "retries": 3, "parallel_runs": 1}', TRUE);