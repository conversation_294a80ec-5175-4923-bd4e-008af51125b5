import React from 'react';
import { useDatabase } from '../../hooks/useDatabase';
import { Wifi, Database, Activity, Clock } from 'lucide-react';

const StatusBar: React.FC = () => {
  const { isConnected, stats } = useDatabase();

  const formatTime = () => {
    return new Date().toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const [currentTime, setCurrentTime] = React.useState(formatTime());

  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(formatTime());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <footer className="bg-white/5 backdrop-blur-sm border-t border-white/10 px-4 py-2">
      <div className="flex items-center justify-between text-sm">
        {/* Left section - System status */}
        <div className="flex items-center space-x-6">
          {/* Database connection */}
          <div className="flex items-center space-x-2">
            <Database className="w-4 h-4 text-gray-400" />
            <span className="text-gray-300">Database:</span>
            <div className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${
                isConnected ? 'bg-green-500' : 'bg-red-500'
              }`} />
              <span className={isConnected ? 'text-green-400' : 'text-red-400'}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>

          {/* API Status */}
          <div className="flex items-center space-x-2">
            <Wifi className="w-4 h-4 text-gray-400" />
            <span className="text-gray-300">API:</span>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 rounded-full bg-yellow-500" />
              <span className="text-yellow-400">Not Configured</span>
            </div>
          </div>

          {/* Activity indicator */}
          <div className="flex items-center space-x-2">
            <Activity className="w-4 h-4 text-gray-400" />
            <span className="text-gray-300">Status:</span>
            <span className="text-green-400">Ready</span>
          </div>
        </div>

        {/* Center section - Stats */}
        {stats && (
          <div className="hidden md:flex items-center space-x-6 text-xs">
            <span className="text-gray-400">
              Models: <span className="text-white font-medium">{stats.models}</span>
            </span>
            <span className="text-gray-400">
              Benchmarks: <span className="text-white font-medium">{stats.benchmarks}</span>
            </span>
            <span className="text-gray-400">
              Test Runs: <span className="text-white font-medium">{stats.testRuns}</span>
            </span>
          </div>
        )}

        {/* Right section - Time and version */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-gray-400" />
            <span className="text-gray-300 font-mono">{currentTime}</span>
          </div>
          <span className="text-gray-400 text-xs">v1.0.0</span>
        </div>
      </div>
    </footer>
  );
};

export default StatusBar;