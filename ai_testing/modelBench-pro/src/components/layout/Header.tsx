import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';

interface HeaderProps {
  onMenuToggle: () => void;
  isMenuOpen: boolean;
}

const Header: React.FC<HeaderProps> = ({ onMenuToggle, isMenuOpen }) => {
  return (
    <header className="bg-white/10 backdrop-blur-sm border-b border-white/20 sticky top-0 z-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Left section - Logo and menu toggle */}
          <div className="flex items-center space-x-4">
            <button
              onClick={onMenuToggle}
              className="p-2 rounded-lg hover:bg-white/10 transition-colors duration-200 lg:hidden"
              aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
            >
              <Menu className="w-5 h-5 text-white" />
            </button>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">MB</span>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-white">ModelBench Pro</h1>
                <p className="text-sm text-gray-300">AI Model Intelligence Evaluation Platform</p>
              </div>
            </div>
          </div>

          {/* Right section - User controls */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <button
              className="p-2 rounded-lg hover:bg-white/10 transition-colors duration-200 relative"
              aria-label="Notifications"
            >
              <Bell className="w-5 h-5 text-white" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs flex items-center justify-center">
                <span className="sr-only">New notifications</span>
              </span>
            </button>

            {/* Settings */}
            <button
              className="p-2 rounded-lg hover:bg-white/10 transition-colors duration-200"
              aria-label="Settings"
            >
              <Settings className="w-5 h-5 text-white" />
            </button>

            {/* User profile */}
            <button
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-white/10 transition-colors duration-200"
              aria-label="User profile"
            >
              <User className="w-5 h-5 text-white" />
              <span className="hidden md:block text-sm text-white">User</span>
            </button>

            {/* Version indicator */}
            <div className="hidden sm:flex items-center space-x-2">
              <span className="text-sm text-gray-300">v1.0.0</span>
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" title="Ready"></div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;