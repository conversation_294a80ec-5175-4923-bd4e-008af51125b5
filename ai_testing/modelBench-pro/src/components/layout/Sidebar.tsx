import React from 'react';
import { X, Home, Database, TestTube, BarChart3, Settings, HelpCircle } from 'lucide-react';
import NavSection from '../navigation/NavSection';
import NavItem from '../navigation/NavItem';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={onClose}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside
        className={`
          fixed top-0 left-0 h-full w-64 bg-white/10 backdrop-blur-sm border-r border-white/20 z-50
          transform transition-transform duration-300 ease-in-out
          lg:relative lg:translate-x-0 lg:z-auto
          ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        `}
      >
        {/* Sidebar header */}
        <div className="flex items-center justify-between p-4 border-b border-white/20 lg:hidden">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">MB</span>
            </div>
            <span className="text-white font-semibold">ModelBench Pro</span>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-white/10 transition-colors duration-200"
            aria-label="Close sidebar"
          >
            <X className="w-5 h-5 text-white" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-6 overflow-y-auto">
          {/* Main Navigation */}
          <NavSection title="Main">
            <NavItem
              icon={Home}
              label="Dashboard"
              href="/"
            />
            <NavItem
              icon={Database}
              label="Models"
              href="/models"
            />
            <NavItem
              icon={TestTube}
              label="Benchmarks"
              href="/benchmarks"
            />
            <NavItem
              icon={BarChart3}
              label="Results"
              href="/results"
            />
          </NavSection>

          {/* Management */}
          <NavSection title="Management">
            <NavItem
              icon={Settings}
              label="Configuration"
              href="/config"
            />
            <NavItem
              icon={Database}
              label="Data Export"
              href="/export"
            />
          </NavSection>

          {/* Support */}
          <NavSection title="Support">
            <NavItem
              icon={HelpCircle}
              label="Help & Documentation"
              href="/help"
            />
          </NavSection>
        </nav>

        {/* Sidebar footer */}
        <div className="p-4 border-t border-white/20">
          <div className="text-xs text-gray-400 text-center">
            <p>ModelBench Pro v1.0.0</p>
            <p className="mt-1">© 2024 ModelBench Team</p>
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;