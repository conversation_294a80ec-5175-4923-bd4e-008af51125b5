import React from 'react';

interface NavSectionProps {
  title: string;
  children: React.ReactNode;
  isCollapsible?: boolean;
  isCollapsed?: boolean;
  onToggle?: () => void;
}

const NavSection: React.FC<NavSectionProps> = ({
  title,
  children,
  isCollapsible = false,
  isCollapsed = false,
  onToggle
}) => {
  return (
    <div className="space-y-2">
      {/* Section header */}
      <div className="flex items-center justify-between">
        <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider px-3">
          {title}
        </h3>
        {isCollapsible && (
          <button
            onClick={onToggle}
            className="p-1 rounded hover:bg-white/10 transition-colors duration-200"
            aria-label={isCollapsed ? `Expand ${title} section` : `Collapse ${title} section`}
          >
            <svg
              className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                isCollapsed ? 'rotate-0' : 'rotate-90'
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        )}
      </div>

      {/* Section content */}
      <div
        className={`
          space-y-1 transition-all duration-200 overflow-hidden
          ${isCollapsed ? 'max-h-0 opacity-0' : 'max-h-96 opacity-100'}
        `}
      >
        {children}
      </div>
    </div>
  );
};

export default NavSection;