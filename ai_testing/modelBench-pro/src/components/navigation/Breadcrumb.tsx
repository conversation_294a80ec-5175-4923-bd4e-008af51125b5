import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  showHome?: boolean;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, showHome = true }) => {

  return (
    <nav aria-label="Breadcrumb" className="flex items-center space-x-2 text-sm">
      {showHome && (
        <>
          <Link
            to="/"
            className="flex items-center text-gray-400 hover:text-white transition-colors duration-200"
            aria-label="Home"
          >
            <Home className="w-4 h-4" />
          </Link>
          {items.length > 0 && (
            <ChevronRight className="w-4 h-4 text-gray-500" />
          )}
        </>
      )}

      {items.map((item, index) => (
        <React.Fragment key={index}>
          {item.href && !item.isActive ? (
            <Link
              to={item.href}
              className="text-gray-400 hover:text-white transition-colors duration-200 truncate max-w-32"
              title={item.label}
            >
              {item.label}
            </Link>
          ) : (
            <span
              className={`truncate max-w-32 ${
                item.isActive ? 'text-white font-medium' : 'text-gray-400'
              }`}
              title={item.label}
            >
              {item.label}
            </span>
          )}

          {index < items.length - 1 && (
            <ChevronRight className="w-4 h-4 text-gray-500 flex-shrink-0" />
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

export default Breadcrumb;