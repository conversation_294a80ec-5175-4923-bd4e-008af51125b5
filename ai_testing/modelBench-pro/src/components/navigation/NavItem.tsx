import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { LucideIcon } from 'lucide-react';

interface NavItemProps {
  icon: LucideIcon;
  label: string;
  href: string;
  isActive?: boolean;
  badge?: string;
  onClick?: () => void;
}

const NavItem: React.FC<NavItemProps> = ({
  icon: Icon,
  label,
  href,
  isActive,
  badge,
  onClick
}) => {
  const location = useLocation();
  const isCurrentPage = isActive !== undefined ? isActive : location.pathname === href;

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  return (
    <Link
      to={href}
      onClick={handleClick}
      className={`
        flex items-center justify-between px-3 py-2 rounded-lg transition-all duration-200
        group hover:bg-white/10 focus:bg-white/10 focus:outline-none focus:ring-2 focus:ring-blue-500/50
        ${isCurrentPage
          ? 'bg-white/20 text-white shadow-lg'
          : 'text-gray-300 hover:text-white'
        }
      `}
      aria-current={isCurrentPage ? 'page' : undefined}
    >
      <div className="flex items-center space-x-3">
        <Icon
          className={`
            w-5 h-5 transition-colors duration-200
            ${isCurrentPage ? 'text-blue-400' : 'text-gray-400 group-hover:text-white'}
          `}
        />
        <span className="font-medium">{label}</span>
      </div>
      
      {badge && (
        <span className={`
          px-2 py-1 text-xs font-semibold rounded-full
          ${isCurrentPage
            ? 'bg-blue-500 text-white'
            : 'bg-white/20 text-gray-300 group-hover:bg-white/30 group-hover:text-white'
          }
        `}>
          {badge}
        </span>
      )}
    </Link>
  );
};

export default NavItem;