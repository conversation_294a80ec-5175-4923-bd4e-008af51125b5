import React from 'react';

type SpinnerSize = 'sm' | 'md' | 'lg' | 'xl';

interface LoadingSpinnerProps {
  size?: SpinnerSize;
  className?: string;
  color?: 'white' | 'blue' | 'purple' | 'gray';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className = '',
  color = 'white'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const colorClasses = {
    white: 'border-white/30 border-t-white',
    blue: 'border-blue-200 border-t-blue-500',
    purple: 'border-purple-200 border-t-purple-500',
    gray: 'border-gray-300 border-t-gray-600'
  };

  const classes = `
    ${sizeClasses[size]}
    ${colorClasses[color]}
    border-2 rounded-full animate-spin
    ${className}
  `.trim();

  return (
    <div
      className={classes}
      role="status"
      aria-label="Loading"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
};

export default LoadingSpinner;