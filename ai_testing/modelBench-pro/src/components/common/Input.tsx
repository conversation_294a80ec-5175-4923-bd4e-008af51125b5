import React, { forwardRef } from 'react';
import { LucideIcon } from 'lucide-react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  icon: Icon,
  iconPosition = 'left',
  fullWidth = false,
  className = '',
  id,
  ...props
}, ref) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  const baseInputClasses = `
    bg-white/10 border rounded-lg px-4 py-3 text-white placeholder-gray-400
    focus:outline-none focus:ring-2 focus:ring-blue-500/20 transition-all duration-200
    backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed
  `;

  const errorClasses = error 
    ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' 
    : 'border-white/20 focus:border-blue-500';

  const iconClasses = Icon ? (iconPosition === 'left' ? 'pl-12' : 'pr-12') : '';

  const inputClasses = `
    ${baseInputClasses}
    ${errorClasses}
    ${iconClasses}
    ${fullWidth ? 'w-full' : ''}
    ${className}
  `.trim();

  return (
    <div className={fullWidth ? 'w-full' : ''}>
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-medium text-gray-300 mb-2"
        >
          {label}
        </label>
      )}
      
      <div className="relative">
        {Icon && (
          <div className={`
            absolute inset-y-0 flex items-center pointer-events-none z-10
            ${iconPosition === 'left' ? 'left-0 pl-4' : 'right-0 pr-4'}
          `}>
            <Icon className="w-5 h-5 text-gray-400" />
          </div>
        )}
        
        <input
          ref={ref}
          id={inputId}
          className={inputClasses}
          {...props}
        />
      </div>
      
      {(error || helperText) && (
        <div className="mt-2">
          {error && (
            <p className="text-sm text-red-400" role="alert">
              {error}
            </p>
          )}
          {helperText && !error && (
            <p className="text-sm text-gray-400">
              {helperText}
            </p>
          )}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;