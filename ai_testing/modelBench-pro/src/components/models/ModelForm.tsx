import React, { useState, useEffect } from 'react';
import { Model, CreateModelInput, UpdateModelInput } from '../../types/database';
import { Save, X } from 'lucide-react';
import Button from '../common/Button';
import Input from '../common/Input';
import Card from '../common/Card';

interface ModelFormProps {
  model?: Model;
  onSubmit: (data: CreateModelInput | UpdateModelInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const ModelForm: React.FC<ModelFormProps> = ({
  model,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const [formData, setFormData] = useState({
    name: '',
    provider: '',
    type: '',
    parameters: '{}'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (model) {
      setFormData({
        name: model.name,
        provider: model.provider,
        type: model.type,
        parameters: model.parameters
      });
    }
  }, [model]);

  const providers = [
    { value: 'openai', label: 'OpenAI' },
    { value: 'anthropic', label: 'Anthropic' },
    { value: 'google', label: 'Google/Gemini' },
    { value: 'ollama', label: 'OLLAMA' },
    { value: 'openrouter', label: 'OpenRouter' }
  ];

  const modelTypes = [
    { value: 'chat', label: 'Chat' },
    { value: 'completion', label: 'Completion' },
    { value: 'embedding', label: 'Embedding' },
    { value: 'image', label: 'Image Generation' },
    { value: 'multimodal', label: 'Multimodal' }
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Model name is required';
    }

    if (!formData.provider) {
      newErrors.provider = 'Provider is required';
    }

    if (!formData.type) {
      newErrors.type = 'Model type is required';
    }

    try {
      JSON.parse(formData.parameters);
    } catch {
      newErrors.parameters = 'Parameters must be valid JSON';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const parameters = JSON.parse(formData.parameters);
      
      if (model) {
        await onSubmit({
          id: model.id,
          name: formData.name,
          provider: formData.provider,
          type: formData.type,
          parameters
        });
      } else {
        await onSubmit({
          name: formData.name,
          provider: formData.provider,
          type: formData.type,
          parameters
        });
      }
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const formatParameters = () => {
    try {
      const parsed = JSON.parse(formData.parameters);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return formData.parameters;
    }
  };

  return (
    <Card>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white">
          {model ? 'Edit Model' : 'Add New Model'}
        </h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          icon={X}
        >
          <span className="sr-only">Cancel</span>
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Model Name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            error={errors.name}
            placeholder="e.g., GPT-4, Claude-3, Gemini Pro"
            required
            fullWidth
          />

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Provider
            </label>
            <select
              value={formData.provider}
              onChange={(e) => handleInputChange('provider', e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 backdrop-blur-sm"
              required
            >
              <option value="" className="bg-gray-800">Select a provider</option>
              {providers.map(provider => (
                <option key={provider.value} value={provider.value} className="bg-gray-800">
                  {provider.label}
                </option>
              ))}
            </select>
            {errors.provider && (
              <p className="mt-2 text-sm text-red-400">{errors.provider}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Model Type
            </label>
            <select
              value={formData.type}
              onChange={(e) => handleInputChange('type', e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 backdrop-blur-sm"
              required
            >
              <option value="" className="bg-gray-800">Select a type</option>
              {modelTypes.map(type => (
                <option key={type.value} value={type.value} className="bg-gray-800">
                  {type.label}
                </option>
              ))}
            </select>
            {errors.type && (
              <p className="mt-2 text-sm text-red-400">{errors.type}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Parameters (JSON)
          </label>
          <textarea
            value={formatParameters()}
            onChange={(e) => handleInputChange('parameters', e.target.value)}
            className="w-full h-32 bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 backdrop-blur-sm font-mono text-sm"
            placeholder='{"temperature": 0.7, "max_tokens": 1000}'
          />
          {errors.parameters && (
            <p className="mt-2 text-sm text-red-400">{errors.parameters}</p>
          )}
          <p className="mt-2 text-sm text-gray-400">
            Enter model-specific parameters as JSON (e.g., temperature, max_tokens, etc.)
          </p>
        </div>

        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-white/20">
          <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
            icon={Save}
          >
            {model ? 'Update Model' : 'Create Model'}
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default ModelForm;