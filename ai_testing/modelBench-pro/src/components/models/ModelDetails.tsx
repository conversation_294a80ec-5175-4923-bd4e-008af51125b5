import React from 'react';
import { Model } from '../../types/database';
import { Calendar, Settings, Database, Edit, Trash2, Play } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';

interface ModelDetailsProps {
  model: Model;
  onEdit?: (model: Model) => void;
  onDelete?: (model: Model) => void;
  onTest?: (model: Model) => void;
  onClose?: () => void;
}

const ModelDetails: React.FC<ModelDetailsProps> = ({
  model,
  onEdit,
  onDelete,
  onTest
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getProviderColor = (provider: string) => {
    const colors: Record<string, string> = {
      'openai': 'bg-green-500',
      'anthropic': 'bg-orange-500',
      'google': 'bg-blue-500',
      'ollama': 'bg-purple-500',
      'openrouter': 'bg-pink-500'
    };
    return colors[provider.toLowerCase()] || 'bg-gray-500';
  };

  const parseParameters = (parametersJson: string) => {
    try {
      return JSON.parse(parametersJson);
    } catch {
      return {};
    }
  };

  const parameters = parseParameters(model.parameters);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <div className={`w-4 h-4 rounded-full ${getProviderColor(model.provider)}`} />
            <div>
              <h1 className="text-2xl font-bold text-white">{model.name}</h1>
              <p className="text-gray-400 mt-1">
                {model.provider} • {model.type}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {onTest && (
              <Button
                variant="primary"
                onClick={() => onTest(model)}
                icon={Play}
              >
                Run Test
              </Button>
            )}
            {onEdit && (
              <Button
                variant="secondary"
                onClick={() => onEdit(model)}
                icon={Edit}
              >
                Edit
              </Button>
            )}
            {onDelete && (
              <Button
                variant="danger"
                onClick={() => onDelete(model)}
                icon={Trash2}
              >
                Delete
              </Button>
            )}
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Basic Information */}
        <Card>
          <div className="flex items-center space-x-2 mb-4">
            <Database className="w-5 h-5 text-blue-400" />
            <h2 className="text-lg font-semibold text-white">Basic Information</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-400">Model ID</label>
              <p className="text-white font-mono">{model.id}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-400">Name</label>
              <p className="text-white">{model.name}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-400">Provider</label>
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${getProviderColor(model.provider)}`} />
                <p className="text-white capitalize">{model.provider}</p>
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-400">Type</label>
              <p className="text-white capitalize">{model.type}</p>
            </div>
          </div>
        </Card>

        {/* Timestamps */}
        <Card>
          <div className="flex items-center space-x-2 mb-4">
            <Calendar className="w-5 h-5 text-green-400" />
            <h2 className="text-lg font-semibold text-white">Timeline</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-400">Created</label>
              <p className="text-white">{formatDate(model.created_at)}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-400">Last Updated</label>
              <p className="text-white">{formatDate(model.updated_at)}</p>
            </div>
          </div>
        </Card>

        {/* Quick Stats */}
        <Card>
          <div className="flex items-center space-x-2 mb-4">
            <Settings className="w-5 h-5 text-purple-400" />
            <h2 className="text-lg font-semibold text-white">Statistics</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-400">Test Runs</label>
              <p className="text-white">0</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-400">Success Rate</label>
              <p className="text-white">-</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-400">Avg Response Time</label>
              <p className="text-white">-</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Parameters */}
      <Card>
        <div className="flex items-center space-x-2 mb-4">
          <Settings className="w-5 h-5 text-yellow-400" />
          <h2 className="text-lg font-semibold text-white">Parameters</h2>
        </div>
        
        {Object.keys(parameters).length > 0 ? (
          <div className="bg-black/20 rounded-lg p-4 overflow-x-auto">
            <pre className="text-sm text-gray-300 font-mono">
              {JSON.stringify(parameters, null, 2)}
            </pre>
          </div>
        ) : (
          <p className="text-gray-400">No parameters configured</p>
        )}
      </Card>

      {/* Recent Activity */}
      <Card>
        <h2 className="text-lg font-semibold text-white mb-4">Recent Activity</h2>
        <div className="text-center py-8">
          <p className="text-gray-400">No recent activity</p>
          <p className="text-sm text-gray-500 mt-2">
            Test runs and benchmarks will appear here
          </p>
        </div>
      </Card>
    </div>
  );
};

export default ModelDetails;