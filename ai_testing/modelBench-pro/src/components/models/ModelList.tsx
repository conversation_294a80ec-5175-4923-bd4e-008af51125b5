import React, { useState, useEffect } from 'react';
import { Model } from '../../types/database';
import { useModels } from '../../hooks/useDatabase';
import { Search, Plus, Grid, List } from 'lucide-react';
import Button from '../common/Button';
import Input from '../common/Input';
import Card from '../common/Card';
import Table, { TableColumn } from '../common/Table';
import LoadingSpinner from '../common/LoadingSpinner';
import ModelCard from './ModelCard';
import ModelForm from './ModelForm';
import ModelDetails from './ModelDetails';
import Modal from '../common/Modal';

type ViewMode = 'grid' | 'list';

interface ModelListProps {
  onModelSelect?: (model: Model) => void;
}

const ModelList: React.FC<ModelListProps> = ({ onModelSelect }) => {
  const {
    models,
    loading,
    error,
    pagination,
    loadModels,
    addModel,
    editModel,
    removeModel
  } = useModels();

  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProvider, setSelectedProvider] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Load models on component mount
  useEffect(() => {
    loadModels();
  }, [loadModels]);

  // Filter models based on search and filters
  const filteredModels = models.filter(model => {
    const matchesSearch = model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         model.provider.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesProvider = !selectedProvider || model.provider === selectedProvider;
    const matchesType = !selectedType || model.type === selectedType;
    
    return matchesSearch && matchesProvider && matchesType;
  });

  const providers = Array.from(new Set(models.map(m => m.provider)));
  const types = Array.from(new Set(models.map(m => m.type)));


  const handleAddModel = () => {
    setSelectedModel(null);
    setIsEditing(false);
    setShowForm(true);
  };

  const handleEditModel = (model: Model) => {
    setSelectedModel(model);
    setIsEditing(true);
    setShowForm(true);
  };

  const handleViewDetails = (model: Model) => {
    setSelectedModel(model);
    setShowDetails(true);
    onModelSelect?.(model);
  };

  const handleDeleteModel = async (model: Model) => {
    if (window.confirm(`Are you sure you want to delete "${model.name}"?`)) {
      await removeModel(model.id);
    }
  };

  const handleFormSubmit = async (data: any) => {
    try {
      if (isEditing && selectedModel) {
        await editModel({ ...data, id: selectedModel.id });
      } else {
        await addModel(data);
      }
      setShowForm(false);
      setSelectedModel(null);
    } catch (error) {
      console.error('Error saving model:', error);
    }
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setSelectedModel(null);
    setIsEditing(false);
  };

  const tableColumns: TableColumn<Model>[] = [
    {
      key: 'name',
      title: 'Name',
      sortable: true,
      render: (value, record) => (
        <button
          onClick={() => handleViewDetails(record)}
          className="text-blue-400 hover:text-blue-300 font-medium"
        >
          {value}
        </button>
      )
    },
    {
      key: 'provider',
      title: 'Provider',
      sortable: true,
      render: (value) => (
        <span className="capitalize">{value}</span>
      )
    },
    {
      key: 'type',
      title: 'Type',
      sortable: true,
      render: (value) => (
        <span className="capitalize">{value}</span>
      )
    },
    {
      key: 'created_at',
      title: 'Created',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditModel(record)}
          >
            Edit
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteModel(record)}
            className="text-red-400 hover:text-red-300"
          >
            Delete
          </Button>
        </div>
      )
    }
  ];

  if (error) {
    return (
      <Card>
        <div className="text-center py-8">
          <p className="text-red-400 mb-4">Error loading models: {error}</p>
          <Button onClick={() => loadModels()}>Retry</Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Models</h1>
          <p className="text-gray-400 mt-1">
            Manage your AI models and configurations
          </p>
        </div>
        <Button
          variant="primary"
          onClick={handleAddModel}
          icon={Plus}
        >
          Add Model
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 flex-1">
            <Input
              placeholder="Search models..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={Search}
              className="flex-1"
            />
            
            <select
              value={selectedProvider}
              onChange={(e) => setSelectedProvider(e.target.value)}
              className="bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            >
              <option value="" className="bg-gray-800">All Providers</option>
              {providers.map(provider => (
                <option key={provider} value={provider} className="bg-gray-800 capitalize">
                  {provider}
                </option>
              ))}
            </select>
            
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            >
              <option value="" className="bg-gray-800">All Types</option>
              {types.map(type => (
                <option key={type} value={type} className="bg-gray-800 capitalize">
                  {type}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              icon={Grid}
            >
              <span className="sr-only">Grid view</span>
            </Button>
            <Button
              variant={viewMode === 'list' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              icon={List}
            >
              <span className="sr-only">List view</span>
            </Button>
          </div>
        </div>
      </Card>

      {/* Content */}
      {loading ? (
        <Card>
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" />
            <span className="ml-3 text-gray-400">Loading models...</span>
          </div>
        </Card>
      ) : filteredModels.length === 0 ? (
        <Card>
          <div className="text-center py-12">
            <p className="text-gray-400 mb-4">
              {models.length === 0 ? 'No models found' : 'No models match your filters'}
            </p>
            {models.length === 0 && (
              <Button onClick={handleAddModel} icon={Plus}>
                Add Your First Model
              </Button>
            )}
          </div>
        </Card>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredModels.map(model => (
            <ModelCard
              key={model.id}
              model={model}
              onEdit={handleEditModel}
              onDelete={handleDeleteModel}
              onTest={() => console.log('Test model:', model.name)}
            />
          ))}
        </div>
      ) : (
        <Table
          columns={tableColumns}
          data={filteredModels}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true
          }}
          onPageChange={(page, pageSize) => loadModels(undefined, page, pageSize)}
        />
      )}

      {/* Modals */}
      <Modal
        isOpen={showForm}
        onClose={handleFormCancel}
        title={isEditing ? 'Edit Model' : 'Add New Model'}
        size="lg"
      >
        <div className="p-6">
          <ModelForm
            {...(selectedModel && { model: selectedModel })}
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
          />
        </div>
      </Modal>

      <Modal
        isOpen={showDetails}
        onClose={() => setShowDetails(false)}
        title="Model Details"
        size="xl"
      >
        <div className="p-6">
          {selectedModel && (
            <ModelDetails
              model={selectedModel}
              onEdit={handleEditModel}
              onDelete={handleDeleteModel}
              onTest={() => console.log('Test model:', selectedModel.name)}
            />
          )}
        </div>
      </Modal>
    </div>
  );
};

export default ModelList;