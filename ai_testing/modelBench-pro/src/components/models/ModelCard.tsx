import React from 'react';
import { Model } from '../../types/database';
import { Edit, Trash2, Play } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';

interface ModelCardProps {
  model: Model;
  onEdit?: (model: Model) => void;
  onDelete?: (model: Model) => void;
  onTest?: (model: Model) => void;
}

const ModelCard: React.FC<ModelCardProps> = ({
  model,
  onEdit,
  onDelete,
  onTest
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getProviderColor = (provider: string) => {
    const colors: Record<string, string> = {
      'openai': 'bg-green-500',
      'anthropic': 'bg-orange-500',
      'google': 'bg-blue-500',
      'ollama': 'bg-purple-500',
      'openrouter': 'bg-pink-500'
    };
    return colors[provider.toLowerCase()] || 'bg-gray-500';
  };

  const parseParameters = (parametersJson: string) => {
    try {
      return JSON.parse(parametersJson);
    } catch {
      return {};
    }
  };

  const parameters = parseParameters(model.parameters);

  return (
    <Card hover className="group">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`w-3 h-3 rounded-full ${getProviderColor(model.provider)}`} />
          <div>
            <h3 className="text-lg font-semibold text-white group-hover:text-blue-400 transition-colors">
              {model.name}
            </h3>
            <p className="text-sm text-gray-400">
              {model.provider} • {model.type}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {onTest && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onTest(model)}
              icon={Play}
              aria-label="Test model"
            >
              <span className="sr-only">Test</span>
            </Button>
          )}
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(model)}
              icon={Edit}
              aria-label="Edit model"
            >
              <span className="sr-only">Edit</span>
            </Button>
          )}
          {onDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(model)}
              icon={Trash2}
              aria-label="Delete model"
              className="text-red-400 hover:text-red-300"
            >
              <span className="sr-only">Delete</span>
            </Button>
          )}
        </div>
      </div>

      {/* Parameters preview */}
      {Object.keys(parameters).length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-300 mb-2">Parameters</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {Object.entries(parameters).slice(0, 4).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="text-gray-400 capitalize">{key}:</span>
                <span className="text-white truncate ml-2">
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </span>
              </div>
            ))}
            {Object.keys(parameters).length > 4 && (
              <div className="col-span-2 text-gray-400 text-center">
                +{Object.keys(parameters).length - 4} more
              </div>
            )}
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="flex items-center justify-between text-xs text-gray-400 pt-4 border-t border-white/10">
        <span>Created {formatDate(model.created_at)}</span>
        <span>Updated {formatDate(model.updated_at)}</span>
      </div>
    </Card>
  );
};

export default ModelCard;