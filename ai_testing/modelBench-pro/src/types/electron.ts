// TypeScript definitions for Electron API exposed via preload script

import {
  Model,
  Benchmark,
  TestRun,
  Result,
  Configuration,
  CreateModelInput,
  CreateBenchmarkInput,
  CreateTestRunInput,
  CreateResultInput,
  CreateConfigurationInput,
  UpdateModelInput,
  UpdateTestRunInput,
  ModelQuery,
  BenchmarkQuery,
  DatabaseResult,
  PaginatedResult,
  DatabaseStats
} from './database';

export interface ElectronAPI {
  // App utilities
  getVersion(): Promise<string>;
  showSaveDialog(options: Electron.SaveDialogOptions): Promise<Electron.SaveDialogReturnValue>;
  showOpenDialog(options: Electron.OpenDialogOptions): Promise<Electron.OpenDialogReturnValue>;

  // Menu event listeners
  onMenuNewTest(callback: () => void): void;
  onMenuOpenResults(callback: () => void): void;
  onMenuExportResults(callback: () => void): void;

  // Database API - Models
  createModel(input: CreateModelInput): Promise<DatabaseResult<Model>>;
  getModels(query?: ModelQuery, page?: number, pageSize?: number): Promise<DatabaseResult<PaginatedResult<Model>>>;
  getModelById(id: number): Promise<DatabaseResult<Model>>;
  updateModel(input: UpdateModelInput): Promise<DatabaseResult<Model>>;
  deleteModel(id: number): Promise<DatabaseResult<void>>;

  // Database API - Benchmarks
  createBenchmark(input: CreateBenchmarkInput): Promise<DatabaseResult<Benchmark>>;
  getBenchmarks(query?: BenchmarkQuery, page?: number, pageSize?: number): Promise<DatabaseResult<PaginatedResult<Benchmark>>>;
  getBenchmarkById(id: number): Promise<DatabaseResult<Benchmark>>;

  // Database API - Test Runs
  createTestRun(input: CreateTestRunInput): Promise<DatabaseResult<TestRun>>;
  getTestRunById(id: number): Promise<DatabaseResult<TestRun>>;
  updateTestRun(input: UpdateTestRunInput): Promise<DatabaseResult<TestRun>>;

  // Database API - Results
  createResult(input: CreateResultInput): Promise<DatabaseResult<Result>>;
  getResultById(id: number): Promise<DatabaseResult<Result>>;

  // Database API - Configurations
  createConfiguration(input: CreateConfigurationInput): Promise<DatabaseResult<Configuration>>;
  getConfigurationById(id: number): Promise<DatabaseResult<Configuration>>;
  getDefaultConfiguration(): Promise<DatabaseResult<Configuration>>;

  // Database API - Utilities
  getDatabaseStats(): Promise<DatabaseResult<DatabaseStats>>;
  backupDatabase(backupPath: string): Promise<DatabaseResult<void>>;
  executeTransaction(operations: TransactionOperation[]): Promise<DatabaseResult<any[]>>;

  // Cleanup
  removeAllListeners(channel: string): void;
}

export interface TransactionOperation {
  method: string;
  args: any[];
}

// Extend the global Window interface
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

// Export for use in React components
export const electronAPI = window.electronAPI;