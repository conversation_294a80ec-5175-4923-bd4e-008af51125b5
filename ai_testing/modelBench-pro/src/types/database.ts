// Database entity interfaces
export interface Model {
  id: number;
  name: string;
  provider: string;
  type: string;
  parameters: string; // JSON string of model parameters
  created_at: string;
  updated_at: string;
}

export interface Benchmark {
  id: number;
  name: string;
  description: string;
  category: string;
  metrics: string; // JSON string of metric definitions
  created_at: string;
}

export interface TestRun {
  id: number;
  model_id: number;
  benchmark_id: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at: string | null;
  results: string | null; // JSON string of aggregated results
}

export interface Result {
  id: number;
  test_run_id: number;
  metric_name: string;
  value: number;
  unit: string;
  timestamp: string;
}

export interface Configuration {
  id: number;
  name: string;
  settings: string; // JSON string of configuration settings
  is_default: boolean;
  created_at: string;
}

// Input types for creating new entities (without auto-generated fields)
export interface CreateModelInput {
  name: string;
  provider: string;
  type: string;
  parameters: Record<string, any>;
}

export interface CreateBenchmarkInput {
  name: string;
  description: string;
  category: string;
  metrics: Record<string, any>;
}

export interface CreateTestRunInput {
  model_id: number;
  benchmark_id: number;
  status?: TestRun['status'];
}

export interface CreateResultInput {
  test_run_id: number;
  metric_name: string;
  value: number;
  unit: string;
}

export interface CreateConfigurationInput {
  name: string;
  settings: Record<string, any>;
  is_default?: boolean;
}

// Update types (partial updates allowed)
export interface UpdateModelInput extends Partial<CreateModelInput> {
  id: number;
}

export interface UpdateBenchmarkInput extends Partial<CreateBenchmarkInput> {
  id: number;
}

export interface UpdateTestRunInput extends Partial<Omit<TestRun, 'id' | 'model_id' | 'benchmark_id'>> {
  id: number;
}

export interface UpdateConfigurationInput extends Partial<CreateConfigurationInput> {
  id: number;
}

// Query parameter types
export interface ModelQuery {
  provider?: string;
  type?: string;
  name?: string;
}

export interface BenchmarkQuery {
  category?: string;
  name?: string;
}

export interface TestRunQuery {
  model_id?: number;
  benchmark_id?: number;
  status?: TestRun['status'];
  date_from?: string;
  date_to?: string;
}

export interface ResultQuery {
  test_run_id?: number;
  metric_name?: string;
  date_from?: string;
  date_to?: string;
}

// Database operation return types
export interface DatabaseResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  rowsAffected?: number;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface DatabaseStats {
  models: number;
  benchmarks: number;
  testRuns: number;
  results: number;
  configurations: number;
}

// Transaction callback type
export type TransactionCallback<T> = () => T;

// Database connection options
export interface DatabaseOptions {
  path: string;
  readonly?: boolean;
  verbose?: boolean;
  timeout?: number;
}

// Migration interface
export interface Migration {
  version: number;
  name: string;
  up: string;
  down: string;
}