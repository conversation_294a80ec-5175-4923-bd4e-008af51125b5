import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from './components/layout/MainLayout';
import ModelList from './components/models/ModelList';
import Breadcrumb from './components/navigation/Breadcrumb';
import Card from './components/common/Card';
import { useDatabase } from './hooks/useDatabase';

// Dashboard Component
const Dashboard: React.FC = () => {
  const { stats, isConnected } = useDatabase();

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Dashboard</h1>
          <Breadcrumb items={[]} showHome={false} />
        </div>
      </div>

      {/* Welcome Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card>
            <h2 className="text-2xl font-bold text-white mb-4">
              Welcome to ModelBench Pro
            </h2>
            <p className="text-gray-300 mb-6">
              A comprehensive cross-platform application for evaluating and comparing AI model 
              performance across diverse domains through standardized prompt testing.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-2">Multi-Provider Support</h3>
                <p className="text-gray-400 text-sm">
                  Test models from OpenAI, Anthropic, Google/Gemini, OpenRouter, and OLLAMA
                </p>
              </div>
              <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-2">Comprehensive Testing</h3>
                <p className="text-gray-400 text-sm">
                  Evaluate across categories including cybersecurity, coding, and reasoning
                </p>
              </div>
              <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-2">Advanced Analytics</h3>
                <p className="text-gray-400 text-sm">
                  Detailed performance metrics, comparisons, and exportable reports
                </p>
              </div>
              <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-2">Cross-Platform</h3>
                <p className="text-gray-400 text-sm">
                  Native desktop application for macOS and Windows
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Status Panel */}
        <div className="space-y-6">
          <Card>
            <h3 className="text-lg font-semibold text-white mb-4">System Status</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Application</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-green-400 text-sm">Ready</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Database</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className={`text-sm ${isConnected ? 'text-green-400' : 'text-red-400'}`}>
                    {isConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">API Clients</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                  <span className="text-gray-400 text-sm">Not Configured</span>
                </div>
              </div>
            </div>
          </Card>

          <Card>
            <h3 className="text-lg font-semibold text-white mb-4">Quick Stats</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Models</span>
                <span className="text-white font-medium">{stats?.models || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Benchmarks</span>
                <span className="text-white font-medium">{stats?.benchmarks || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Test Runs</span>
                <span className="text-white font-medium">{stats?.testRuns || 0}</span>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Development Phase Indicator */}
      <div className="text-center">
        <div className="inline-flex items-center space-x-2 bg-blue-500/20 text-blue-300 px-4 py-2 rounded-full border border-blue-500/30">
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">Phase 3: Core UI Components - TASK_003 Complete</span>
        </div>
      </div>
    </div>
  );
};

// Models Page Component
const ModelsPage: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <Breadcrumb 
          items={[
            { label: 'Models', isActive: true }
          ]} 
        />
      </div>
      <ModelList />
    </div>
  );
};

// Benchmarks Page Component
const BenchmarksPage: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <Breadcrumb 
          items={[
            { label: 'Benchmarks', isActive: true }
          ]} 
        />
      </div>
      <Card>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-white mb-4">Benchmarks</h2>
          <p className="text-gray-400">Benchmark management will be implemented in future tasks.</p>
        </div>
      </Card>
    </div>
  );
};

// Results Page Component
const ResultsPage: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <Breadcrumb 
          items={[
            { label: 'Results', isActive: true }
          ]} 
        />
      </div>
      <Card>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-white mb-4">Test Results</h2>
          <p className="text-gray-400">Test results and analytics will be implemented in future tasks.</p>
        </div>
      </Card>
    </div>
  );
};

// Configuration Page Component
const ConfigPage: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <Breadcrumb 
          items={[
            { label: 'Configuration', isActive: true }
          ]} 
        />
      </div>
      <Card>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-white mb-4">Configuration</h2>
          <p className="text-gray-400">Application configuration will be implemented in future tasks.</p>
        </div>
      </Card>
    </div>
  );
};

/**
 * Main Application Component
 * Updated to use the new layout system with routing
 */
const App: React.FC = () => {
  return (
    <Router>
      <MainLayout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/models" element={<ModelsPage />} />
          <Route path="/benchmarks" element={<BenchmarksPage />} />
          <Route path="/results" element={<ResultsPage />} />
          <Route path="/config" element={<ConfigPage />} />
          <Route path="/export" element={<Navigate to="/config" replace />} />
          <Route path="/help" element={<Navigate to="/" replace />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </MainLayout>
    </Router>
  );
};

export default App;