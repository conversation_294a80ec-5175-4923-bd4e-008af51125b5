// Custom database error classes for ModelBench Pro

export class DatabaseError extends Error {
  public readonly code: string;
  public readonly details?: any;

  constructor(message: string, code: string = 'DATABASE_ERROR', details?: any) {
    super(message);
    this.name = 'DatabaseError';
    this.code = code;
    this.details = details;
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, DatabaseError);
    }
  }
}

export class DatabaseConnectionError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'CONNECTION_ERROR', details);
    this.name = 'DatabaseConnectionError';
  }
}

export class DatabaseInitializationError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'INITIALIZATION_ERROR', details);
    this.name = 'DatabaseInitializationError';
  }
}

export class DatabaseMigrationError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'MIGRATION_ERROR', details);
    this.name = 'DatabaseMigrationError';
  }
}

export class DatabaseTransactionError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'TRANSACTION_ERROR', details);
    this.name = 'DatabaseTransactionError';
  }
}

export class DatabaseValidationError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details);
    this.name = 'DatabaseValidationError';
  }
}

export class DatabaseNotFoundError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'NOT_FOUND_ERROR', details);
    this.name = 'DatabaseNotFoundError';
  }
}

export class DatabaseConstraintError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'CONSTRAINT_ERROR', details);
    this.name = 'DatabaseConstraintError';
  }
}

// Error handler utility function
export function handleDatabaseError(error: any): DatabaseError {
  if (error instanceof DatabaseError) {
    return error;
  }

  // Handle SQLite specific errors
  if (error.code) {
    switch (error.code) {
      case 'SQLITE_CANTOPEN':
        return new DatabaseConnectionError(
          'Cannot open database file',
          { originalError: error }
        );
      case 'SQLITE_CORRUPT':
        return new DatabaseError(
          'Database file is corrupted',
          'CORRUPTION_ERROR',
          { originalError: error }
        );
      case 'SQLITE_CONSTRAINT':
        return new DatabaseConstraintError(
          'Database constraint violation',
          { originalError: error }
        );
      case 'SQLITE_BUSY':
        return new DatabaseError(
          'Database is busy',
          'BUSY_ERROR',
          { originalError: error }
        );
      case 'SQLITE_LOCKED':
        return new DatabaseError(
          'Database is locked',
          'LOCKED_ERROR',
          { originalError: error }
        );
      default:
        return new DatabaseError(
          error.message || 'Unknown database error',
          error.code,
          { originalError: error }
        );
    }
  }

  // Handle generic errors
  return new DatabaseError(
    error.message || 'Unknown database error',
    'UNKNOWN_ERROR',
    { originalError: error }
  );
}

// Retry utility for database operations
export async function retryDatabaseOperation<T>(
  operation: () => T,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error | undefined;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return operation();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry certain types of errors
      if (error instanceof DatabaseValidationError ||
          error instanceof DatabaseConstraintError ||
          error instanceof DatabaseNotFoundError) {
        throw error;
      }

      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delayMs * attempt));
    }
  }

  throw new DatabaseError(
    `Operation failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`,
    'RETRY_EXHAUSTED',
    { originalError: lastError, attempts: maxRetries }
  );
}