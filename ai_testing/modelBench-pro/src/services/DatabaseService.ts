import Database from 'better-sqlite3';
import { readFileSync } from 'fs';
import { join } from 'path';
import {
  Model,
  Benchmark,
  TestRun,
  Result,
  Configuration,
  CreateModelInput,
  CreateBenchmarkInput,
  CreateTestRunInput,
  CreateResultInput,
  CreateConfigurationInput,
  UpdateModelInput,
  UpdateTestRunInput,
  ModelQuery,
  BenchmarkQuery,
  DatabaseResult,
  PaginatedResult,
  DatabaseStats,
  TransactionCallback,
  DatabaseOptions
} from '../types/database';
import {
  DatabaseError,
  DatabaseInitializationError,
  DatabaseMigrationError,
  DatabaseValidationError,
  handleDatabaseError
} from './DatabaseErrors';

export class DatabaseService {
  private db: Database.Database | null = null;
  private isInitialized = false;
  private readonly options: DatabaseOptions;

  // Prepared statements for performance
  private statements: { [key: string]: Database.Statement } = {};

  constructor(options: DatabaseOptions) {
    this.options = {
      readonly: false,
      verbose: false,
      timeout: 30000,
      ...options
    };
  }

  /**
   * Initialize the database connection and schema
   */
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        return;
      }

      // Create database connection
      this.db = new Database(this.options.path, {
        readonly: this.options.readonly,
        verbose: this.options.verbose ? console.log : undefined,
        timeout: this.options.timeout
      });

      // Configure database settings
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = 1000');
      this.db.pragma('temp_store = memory');
      this.db.pragma('foreign_keys = ON');

      // Load and execute schema
      await this.executeSchema();

      // Prepare commonly used statements
      this.prepareStatements();

      // Run any pending migrations
      await this.runMigrations();

      this.isInitialized = true;
    } catch (error) {
      throw new DatabaseInitializationError(
        `Failed to initialize database: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { path: this.options.path, error }
      );
    }
  }

  /**
   * Close the database connection
   */
  close(): void {
    if (this.db) {
      try {
        // Clear prepared statements
        this.statements = {};

        this.db.close();
        this.db = null;
        this.isInitialized = false;
      } catch (error) {
        throw handleDatabaseError(error);
      }
    }
  }

  /**
   * Execute a transaction with automatic rollback on error
   */
  transaction<T>(callback: TransactionCallback<T>): DatabaseResult<T> {
    this.ensureInitialized();
    
    try {
      const transaction = this.db!.transaction(callback);
      const result = transaction();
      return { success: true, data: result };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  // MODEL OPERATIONS

  /**
   * Create a new model
   */
  createModel(input: CreateModelInput): DatabaseResult<Model> {
    this.ensureInitialized();
    
    try {
      this.validateModelInput(input);
      
      const stmt = this.statements.createModel;
      const info = stmt.run(
        input.name,
        input.provider,
        input.type,
        JSON.stringify(input.parameters)
      );
      
      const model = this.getModelById(info.lastInsertRowid as number);
      return { success: true, data: model.data!, rowsAffected: info.changes };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Get model by ID
   */
  getModelById(id: number): DatabaseResult<Model> {
    this.ensureInitialized();
    
    try {
      const stmt = this.statements.getModelById;
      const row = stmt.get(id) as Model | undefined;
      
      if (!row) {
        return { success: false, error: `Model with ID ${id} not found` };
      }
      
      return { success: true, data: this.parseModelRow(row) };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Get models with optional filtering and pagination
   */
  getModels(query: ModelQuery = {}, page = 1, pageSize = 50): DatabaseResult<PaginatedResult<Model>> {
    this.ensureInitialized();
    
    try {
      const { whereClause, params } = this.buildModelWhereClause(query);
      const offset = (page - 1) * pageSize;
      
      // Get total count
      const countSql = `SELECT COUNT(*) as count FROM models ${whereClause}`;
      const countResult = this.db!.prepare(countSql).get(...params) as { count: number };
      const total = countResult.count;
      
      // Get paginated results
      const dataSql = `SELECT * FROM models ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`;
      const rows = this.db!.prepare(dataSql).all(...params, pageSize, offset) as Model[];
      
      const data = rows.map(row => this.parseModelRow(row));
      
      return {
        success: true,
        data: {
          data,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Update a model
   */
  updateModel(input: UpdateModelInput): DatabaseResult<Model> {
    this.ensureInitialized();
    
    try {
      const { id, ...updates } = input;
      
      if (Object.keys(updates).length === 0) {
        return { success: false, error: 'No fields to update' };
      }
      
      const setClauses: string[] = [];
      const params: any[] = [];
      
      if (updates.name !== undefined) {
        setClauses.push('name = ?');
        params.push(updates.name);
      }
      if (updates.provider !== undefined) {
        setClauses.push('provider = ?');
        params.push(updates.provider);
      }
      if (updates.type !== undefined) {
        setClauses.push('type = ?');
        params.push(updates.type);
      }
      if (updates.parameters !== undefined) {
        setClauses.push('parameters = ?');
        params.push(JSON.stringify(updates.parameters));
      }
      
      setClauses.push('updated_at = CURRENT_TIMESTAMP');
      params.push(id);
      
      const sql = `UPDATE models SET ${setClauses.join(', ')} WHERE id = ?`;
      const stmt = this.db!.prepare(sql);
      const info = stmt.run(...params);
      
      if (info.changes === 0) {
        return { success: false, error: `Model with ID ${id} not found` };
      }
      
      const model = this.getModelById(id);
      return { success: true, data: model.data!, rowsAffected: info.changes };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Delete a model
   */
  deleteModel(id: number): DatabaseResult<void> {
    this.ensureInitialized();
    
    try {
      const stmt = this.statements.deleteModel;
      const info = stmt.run(id);
      
      if (info.changes === 0) {
        return { success: false, error: `Model with ID ${id} not found` };
      }
      
      return { success: true, rowsAffected: info.changes };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  // BENCHMARK OPERATIONS

  /**
   * Create a new benchmark
   */
  createBenchmark(input: CreateBenchmarkInput): DatabaseResult<Benchmark> {
    this.ensureInitialized();
    
    try {
      this.validateBenchmarkInput(input);
      
      const stmt = this.statements.createBenchmark;
      const info = stmt.run(
        input.name,
        input.description,
        input.category,
        JSON.stringify(input.metrics)
      );
      
      const benchmark = this.getBenchmarkById(info.lastInsertRowid as number);
      return { success: true, data: benchmark.data!, rowsAffected: info.changes };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Get benchmark by ID
   */
  getBenchmarkById(id: number): DatabaseResult<Benchmark> {
    this.ensureInitialized();
    
    try {
      const stmt = this.statements.getBenchmarkById;
      const row = stmt.get(id) as Benchmark | undefined;
      
      if (!row) {
        return { success: false, error: `Benchmark with ID ${id} not found` };
      }
      
      return { success: true, data: this.parseBenchmarkRow(row) };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Get benchmarks with optional filtering and pagination
   */
  getBenchmarks(query: BenchmarkQuery = {}, page = 1, pageSize = 50): DatabaseResult<PaginatedResult<Benchmark>> {
    this.ensureInitialized();
    
    try {
      const { whereClause, params } = this.buildBenchmarkWhereClause(query);
      const offset = (page - 1) * pageSize;
      
      // Get total count
      const countSql = `SELECT COUNT(*) as count FROM benchmarks ${whereClause}`;
      const countResult = this.db!.prepare(countSql).get(...params) as { count: number };
      const total = countResult.count;
      
      // Get paginated results
      const dataSql = `SELECT * FROM benchmarks ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`;
      const rows = this.db!.prepare(dataSql).all(...params, pageSize, offset) as Benchmark[];
      
      const data = rows.map(row => this.parseBenchmarkRow(row));
      
      return {
        success: true,
        data: {
          data,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  // TEST RUN OPERATIONS

  /**
   * Create a new test run
   */
  createTestRun(input: CreateTestRunInput): DatabaseResult<TestRun> {
    this.ensureInitialized();
    
    try {
      this.validateTestRunInput(input);
      
      const stmt = this.statements.createTestRun;
      const info = stmt.run(
        input.model_id,
        input.benchmark_id,
        input.status || 'pending'
      );
      
      const testRun = this.getTestRunById(info.lastInsertRowid as number);
      return { success: true, data: testRun.data!, rowsAffected: info.changes };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Get test run by ID
   */
  getTestRunById(id: number): DatabaseResult<TestRun> {
    this.ensureInitialized();
    
    try {
      const stmt = this.statements.getTestRunById;
      const row = stmt.get(id) as TestRun | undefined;
      
      if (!row) {
        return { success: false, error: `Test run with ID ${id} not found` };
      }
      
      return { success: true, data: this.parseTestRunRow(row) };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Update test run status and results
   */
  updateTestRun(input: UpdateTestRunInput): DatabaseResult<TestRun> {
    this.ensureInitialized();
    
    try {
      const { id, ...updates } = input;
      
      if (Object.keys(updates).length === 0) {
        return { success: false, error: 'No fields to update' };
      }
      
      const setClauses: string[] = [];
      const params: any[] = [];
      
      if (updates.status !== undefined) {
        setClauses.push('status = ?');
        params.push(updates.status);
      }
      if (updates.completed_at !== undefined) {
        setClauses.push('completed_at = ?');
        params.push(updates.completed_at);
      }
      if (updates.results !== undefined) {
        setClauses.push('results = ?');
        params.push(updates.results);
      }
      
      params.push(id);
      
      const sql = `UPDATE test_runs SET ${setClauses.join(', ')} WHERE id = ?`;
      const stmt = this.db!.prepare(sql);
      const info = stmt.run(...params);
      
      if (info.changes === 0) {
        return { success: false, error: `Test run with ID ${id} not found` };
      }
      
      const testRun = this.getTestRunById(id);
      return { success: true, data: testRun.data!, rowsAffected: info.changes };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  // RESULT OPERATIONS

  /**
   * Create a new result
   */
  createResult(input: CreateResultInput): DatabaseResult<Result> {
    this.ensureInitialized();
    
    try {
      this.validateResultInput(input);
      
      const stmt = this.statements.createResult;
      const info = stmt.run(
        input.test_run_id,
        input.metric_name,
        input.value,
        input.unit
      );
      
      const result = this.getResultById(info.lastInsertRowid as number);
      return { success: true, data: result.data!, rowsAffected: info.changes };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Get result by ID
   */
  getResultById(id: number): DatabaseResult<Result> {
    this.ensureInitialized();
    
    try {
      const stmt = this.statements.getResultById;
      const row = stmt.get(id) as Result | undefined;
      
      if (!row) {
        return { success: false, error: `Result with ID ${id} not found` };
      }
      
      return { success: true, data: row };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  // CONFIGURATION OPERATIONS

  /**
   * Create a new configuration
   */
  createConfiguration(input: CreateConfigurationInput): DatabaseResult<Configuration> {
    this.ensureInitialized();
    
    try {
      this.validateConfigurationInput(input);
      
      const stmt = this.statements.createConfiguration;
      const info = stmt.run(
        input.name,
        JSON.stringify(input.settings),
        input.is_default || false
      );
      
      const config = this.getConfigurationById(info.lastInsertRowid as number);
      return { success: true, data: config.data!, rowsAffected: info.changes };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Get configuration by ID
   */
  getConfigurationById(id: number): DatabaseResult<Configuration> {
    this.ensureInitialized();
    
    try {
      const stmt = this.statements.getConfigurationById;
      const row = stmt.get(id) as Configuration | undefined;
      
      if (!row) {
        return { success: false, error: `Configuration with ID ${id} not found` };
      }
      
      return { success: true, data: this.parseConfigurationRow(row) };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Get default configuration
   */
  getDefaultConfiguration(): DatabaseResult<Configuration> {
    this.ensureInitialized();
    
    try {
      const stmt = this.statements.getDefaultConfiguration;
      const row = stmt.get() as Configuration | undefined;
      
      if (!row) {
        return { success: false, error: 'No default configuration found' };
      }
      
      return { success: true, data: this.parseConfigurationRow(row) };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  // UTILITY METHODS

  /**
   * Get database statistics
   */
  getStats(): DatabaseResult<DatabaseStats> {
    this.ensureInitialized();
    
    try {
      const stats = {
        models: (this.db!.prepare('SELECT COUNT(*) as count FROM models').get() as { count: number }).count,
        benchmarks: (this.db!.prepare('SELECT COUNT(*) as count FROM benchmarks').get() as { count: number }).count,
        testRuns: (this.db!.prepare('SELECT COUNT(*) as count FROM test_runs').get() as { count: number }).count,
        results: (this.db!.prepare('SELECT COUNT(*) as count FROM results').get() as { count: number }).count,
        configurations: (this.db!.prepare('SELECT COUNT(*) as count FROM configurations').get() as { count: number }).count
      };
      
      return { success: true, data: stats };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  /**
   * Backup database to a file
   */
  backup(backupPath: string): DatabaseResult<void> {
    this.ensureInitialized();
    
    try {
      this.db!.backup(backupPath);
      return { success: true };
    } catch (error) {
      const dbError = handleDatabaseError(error);
      return { success: false, error: dbError.message };
    }
  }

  // PRIVATE METHODS

  private ensureInitialized(): void {
    if (!this.isInitialized || !this.db) {
      throw new DatabaseError('Database not initialized. Call initialize() first.');
    }
  }

  private async executeSchema(): Promise<void> {
    try {
      const schemaPath = join(__dirname, '../data/schema.sql');
      const schema = readFileSync(schemaPath, 'utf-8');
      this.db!.exec(schema);
    } catch (error) {
      throw new DatabaseInitializationError(
        `Failed to execute database schema: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { error }
      );
    }
  }

  private prepareStatements(): void {
    try {
      // Model statements
      this.statements.createModel = this.db!.prepare(
        'INSERT INTO models (name, provider, type, parameters) VALUES (?, ?, ?, ?)'
      );
      this.statements.getModelById = this.db!.prepare('SELECT * FROM models WHERE id = ?');
      this.statements.deleteModel = this.db!.prepare('DELETE FROM models WHERE id = ?');

      // Benchmark statements
      this.statements.createBenchmark = this.db!.prepare(
        'INSERT INTO benchmarks (name, description, category, metrics) VALUES (?, ?, ?, ?)'
      );
      this.statements.getBenchmarkById = this.db!.prepare('SELECT * FROM benchmarks WHERE id = ?');

      // Test run statements
      this.statements.createTestRun = this.db!.prepare(
        'INSERT INTO test_runs (model_id, benchmark_id, status) VALUES (?, ?, ?)'
      );
      this.statements.getTestRunById = this.db!.prepare('SELECT * FROM test_runs WHERE id = ?');

      // Result statements
      this.statements.createResult = this.db!.prepare(
        'INSERT INTO results (test_run_id, metric_name, value, unit) VALUES (?, ?, ?, ?)'
      );
      this.statements.getResultById = this.db!.prepare('SELECT * FROM results WHERE id = ?');

      // Configuration statements
      this.statements.createConfiguration = this.db!.prepare(
        'INSERT INTO configurations (name, settings, is_default) VALUES (?, ?, ?)'
      );
      this.statements.getConfigurationById = this.db!.prepare('SELECT * FROM configurations WHERE id = ?');
      this.statements.getDefaultConfiguration = this.db!.prepare(
        'SELECT * FROM configurations WHERE is_default = TRUE LIMIT 1'
      );
    } catch (error) {
      throw new DatabaseInitializationError(
        `Failed to prepare statements: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { error }
      );
    }
  }

  private async runMigrations(): Promise<void> {
    // Migration logic would go here
    // For now, we'll just ensure the schema version is set
    try {
      const version = this.db!.prepare('SELECT value FROM db_metadata WHERE key = ?').get('schema_version');
      if (!version) {
        this.db!.prepare('INSERT INTO db_metadata (key, value) VALUES (?, ?)').run('schema_version', '1.0.0');
      }
    } catch (error) {
      throw new DatabaseMigrationError(
        `Failed to run migrations: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { error }
      );
    }
  }

  // Validation methods
  private validateModelInput(input: CreateModelInput): void {
    if (!input.name?.trim()) {
      throw new DatabaseValidationError('Model name is required');
    }
    if (!input.provider?.trim()) {
      throw new DatabaseValidationError('Model provider is required');
    }
    if (!input.type?.trim()) {
      throw new DatabaseValidationError('Model type is required');
    }
    if (!input.parameters || typeof input.parameters !== 'object') {
      throw new DatabaseValidationError('Model parameters must be an object');
    }
  }

  private validateBenchmarkInput(input: CreateBenchmarkInput): void {
    if (!input.name?.trim()) {
      throw new DatabaseValidationError('Benchmark name is required');
    }
    if (!input.description?.trim()) {
      throw new DatabaseValidationError('Benchmark description is required');
    }
    if (!input.category?.trim()) {
      throw new DatabaseValidationError('Benchmark category is required');
    }
    if (!input.metrics || typeof input.metrics !== 'object') {
      throw new DatabaseValidationError('Benchmark metrics must be an object');
    }
  }

  private validateTestRunInput(input: CreateTestRunInput): void {
    if (!Number.isInteger(input.model_id) || input.model_id <= 0) {
      throw new DatabaseValidationError('Valid model ID is required');
    }
    if (!Number.isInteger(input.benchmark_id) || input.benchmark_id <= 0) {
      throw new DatabaseValidationError('Valid benchmark ID is required');
    }
  }

  private validateResultInput(input: CreateResultInput): void {
    if (!Number.isInteger(input.test_run_id) || input.test_run_id <= 0) {
      throw new DatabaseValidationError('Valid test run ID is required');
    }
    if (!input.metric_name?.trim()) {
      throw new DatabaseValidationError('Metric name is required');
    }
    if (typeof input.value !== 'number') {
      throw new DatabaseValidationError('Result value must be a number');
    }
    if (!input.unit?.trim()) {
      throw new DatabaseValidationError('Result unit is required');
    }
  }

  private validateConfigurationInput(input: CreateConfigurationInput): void {
    if (!input.name?.trim()) {
      throw new DatabaseValidationError('Configuration name is required');
    }
    if (!input.settings || typeof input.settings !== 'object') {
      throw new DatabaseValidationError('Configuration settings must be an object');
    }
  }

  // Row parsing methods
  private parseModelRow(row: any): Model {
    return {
      ...row,
      parameters: JSON.parse(row.parameters)
    };
  }

  private parseBenchmarkRow(row: any): Benchmark {
    return {
      ...row,
      metrics: JSON.parse(row.metrics)
    };
  }

  private parseTestRunRow(row: any): TestRun {
    return {
      ...row,
      results: row.results ? JSON.parse(row.results) : null
    };
  }

  private parseConfigurationRow(row: any): Configuration {
    return {
      ...row,
      settings: JSON.parse(row.settings)
    };
  }

  // Query building methods
  private buildModelWhereClause(query: ModelQuery): { whereClause: string; params: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];

    if (query.provider) {
      conditions.push('provider = ?');
      params.push(query.provider);
    }
    if (query.type) {
      conditions.push('type = ?');
      params.push(query.type);
    }
    if (query.name) {
      conditions.push('name LIKE ?');
      params.push(`%${query.name}%`);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { whereClause, params };
  }

  private buildBenchmarkWhereClause(query: BenchmarkQuery): { whereClause: string; params: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];

    if (query.category) {
      conditions.push('category = ?');
      params.push(query.category);
    }
    if (query.name) {
      conditions.push('name LIKE ?');
      params.push(`%${query.name}%`);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { whereClause, params };
  }
}

// Export singleton instance
let databaseInstance: DatabaseService | null = null;

export function getDatabaseInstance(options?: DatabaseOptions): DatabaseService {
  if (!databaseInstance && options) {
    databaseInstance = new DatabaseService(options);
  }
  if (!databaseInstance) {
    throw new DatabaseError('Database instance not initialized. Provide options on first call.');
  }
  return databaseInstance;
}

export function closeDatabaseInstance(): void {
  if (databaseInstance) {
    databaseInstance.close();
    databaseInstance = null;
  }
}