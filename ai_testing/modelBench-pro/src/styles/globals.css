/* TailwindCSS imports */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles and CSS custom properties */
:root {
  /* Color scheme variables */
  --color-primary: #3b82f6;
  --color-primary-dark: #1d4ed8;
  --color-secondary: #a855f7;
  --color-secondary-dark: #7c3aed;
  --color-success: #22c55e;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  
  /* Glass morphism variables */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Animation durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  
  /* Z-index scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Remove default button styles */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* Remove default input styles */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Selection styles */
::selection {
  background: rgba(59, 130, 246, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(59, 130, 246, 0.3);
  color: white;
}

/* Custom component styles */
@layer components {
  /* Glass morphism card */
  .glass-card {
    @apply bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl;
    box-shadow: var(--glass-shadow);
  }
  
  /* Button variants */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-medium;
    @apply hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg;
    @apply focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-transparent;
  }
  
  .btn-secondary {
    @apply bg-white/10 text-white px-6 py-3 rounded-lg font-medium border border-white/20;
    @apply hover:bg-white/20 transition-all duration-200;
    @apply focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent;
  }
  
  .btn-ghost {
    @apply text-white px-4 py-2 rounded-lg font-medium;
    @apply hover:bg-white/10 transition-all duration-200;
    @apply focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-transparent;
  }
  
  /* Input styles */
  .input-primary {
    @apply bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400;
    @apply focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200;
    @apply backdrop-blur-sm;
  }
  
  /* Status indicators */
  .status-indicator {
    @apply w-2 h-2 rounded-full;
  }
  
  .status-success {
    @apply bg-green-500;
  }
  
  .status-warning {
    @apply bg-yellow-500;
  }
  
  .status-error {
    @apply bg-red-500;
  }
  
  .status-info {
    @apply bg-blue-500;
  }
  
  /* Loading spinner */
  .spinner {
    @apply w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin;
  }
  
  /* Text gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent;
  }
  
  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-2xl;
  }
}

/* Utility classes */
@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn var(--duration-normal) ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp var(--duration-normal) ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown var(--duration-normal) ease-out;
  }
  
  /* Glass morphism utilities */
  .backdrop-blur-glass {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  /* Text utilities */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  /* Layout utilities */
  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Print styles */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-card {
    @apply border-2 border-white;
  }
  
  .btn-primary {
    @apply border-2 border-white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .animate-pulse {
    animation: none;
  }
  
  .animate-spin {
    animation: none;
  }
}