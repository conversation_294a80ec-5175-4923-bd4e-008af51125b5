const { app, <PERSON>rowserWindow, Menu, ipc<PERSON>ain, dialog } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

// Database imports
const { getDatabaseInstance, closeDatabaseInstance } = require('./src/services/DatabaseService');
const { DatabaseError } = require('./src/services/DatabaseErrors');

/**
 * Main application window instance
 * @type {BrowserWindow | null}
 */
let mainWindow = null;

/**
 * Database instance
 * @type {any | null}
 */
let database = null;

/**
 * Initialize the database
 * @returns {Promise<void>}
 */
async function initializeDatabase() {
  try {
    // Get user data directory for database storage
    const userDataPath = app.getPath('userData');
    const dbPath = path.join(userDataPath, 'modelbench.db');
    
    console.log('Initializing database at:', dbPath);
    
    // Create database instance
    database = getDatabaseInstance({
      path: dbPath,
      verbose: isDev,
      timeout: 30000
    });
    
    // Initialize database schema and connections
    await database.initialize();
    
    console.log('Database initialized successfully');
    
    // Set up IPC handlers for database operations
    setupDatabaseIPC();
    
  } catch (error) {
    console.error('Failed to initialize database:', error);
    
    // Show error dialog to user
    dialog.showErrorBox(
      'Database Error',
      `Failed to initialize database: ${error.message}\n\nThe application may not function properly.`
    );
    
    throw error;
  }
}

/**
 * Setup IPC handlers for database operations
 */
function setupDatabaseIPC() {
  // Model operations
  ipcMain.handle('db-create-model', async (event, input) => {
    try {
      return database.createModel(input);
    } catch (error) {
      console.error('Database error in create-model:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-get-models', async (event, query, page, pageSize) => {
    try {
      return database.getModels(query, page, pageSize);
    } catch (error) {
      console.error('Database error in get-models:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-get-model-by-id', async (event, id) => {
    try {
      return database.getModelById(id);
    } catch (error) {
      console.error('Database error in get-model-by-id:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-update-model', async (event, input) => {
    try {
      return database.updateModel(input);
    } catch (error) {
      console.error('Database error in update-model:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-delete-model', async (event, id) => {
    try {
      return database.deleteModel(id);
    } catch (error) {
      console.error('Database error in delete-model:', error);
      return { success: false, error: error.message };
    }
  });
  
  // Benchmark operations
  ipcMain.handle('db-create-benchmark', async (event, input) => {
    try {
      return database.createBenchmark(input);
    } catch (error) {
      console.error('Database error in create-benchmark:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-get-benchmarks', async (event, query, page, pageSize) => {
    try {
      return database.getBenchmarks(query, page, pageSize);
    } catch (error) {
      console.error('Database error in get-benchmarks:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-get-benchmark-by-id', async (event, id) => {
    try {
      return database.getBenchmarkById(id);
    } catch (error) {
      console.error('Database error in get-benchmark-by-id:', error);
      return { success: false, error: error.message };
    }
  });
  
  // Test run operations
  ipcMain.handle('db-create-test-run', async (event, input) => {
    try {
      return database.createTestRun(input);
    } catch (error) {
      console.error('Database error in create-test-run:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-get-test-run-by-id', async (event, id) => {
    try {
      return database.getTestRunById(id);
    } catch (error) {
      console.error('Database error in get-test-run-by-id:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-update-test-run', async (event, input) => {
    try {
      return database.updateTestRun(input);
    } catch (error) {
      console.error('Database error in update-test-run:', error);
      return { success: false, error: error.message };
    }
  });
  
  // Result operations
  ipcMain.handle('db-create-result', async (event, input) => {
    try {
      return database.createResult(input);
    } catch (error) {
      console.error('Database error in create-result:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-get-result-by-id', async (event, id) => {
    try {
      return database.getResultById(id);
    } catch (error) {
      console.error('Database error in get-result-by-id:', error);
      return { success: false, error: error.message };
    }
  });
  
  // Configuration operations
  ipcMain.handle('db-create-configuration', async (event, input) => {
    try {
      return database.createConfiguration(input);
    } catch (error) {
      console.error('Database error in create-configuration:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-get-configuration-by-id', async (event, id) => {
    try {
      return database.getConfigurationById(id);
    } catch (error) {
      console.error('Database error in get-configuration-by-id:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-get-default-configuration', async (event) => {
    try {
      return database.getDefaultConfiguration();
    } catch (error) {
      console.error('Database error in get-default-configuration:', error);
      return { success: false, error: error.message };
    }
  });
  
  // Utility operations
  ipcMain.handle('db-get-stats', async (event) => {
    try {
      return database.getStats();
    } catch (error) {
      console.error('Database error in get-stats:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-backup', async (event, backupPath) => {
    try {
      return database.backup(backupPath);
    } catch (error) {
      console.error('Database error in backup:', error);
      return { success: false, error: error.message };
    }
  });
  
  ipcMain.handle('db-transaction', async (event, operations) => {
    try {
      return database.transaction(() => {
        // Execute multiple operations in a transaction
        const results = [];
        for (const operation of operations) {
          const result = database[operation.method](...operation.args);
          results.push(result);
        }
        return results;
      });
    } catch (error) {
      console.error('Database error in transaction:', error);
      return { success: false, error: error.message };
    }
  });
}

/**
 * Cleanup database on app exit
 */
function cleanupDatabase() {
  if (database) {
    try {
      console.log('Closing database connection...');
      closeDatabaseInstance();
      database = null;
      console.log('Database connection closed successfully');
    } catch (error) {
      console.error('Error closing database:', error);
    }
  }
}

/**
 * Creates the main application window
 * @returns {BrowserWindow} The created window instance
 */
function createMainWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'), // We'll create this later
      webSecurity: true,
    },
    icon: path.join(__dirname, 'public/icon.png'), // We'll add this later
    show: false, // Don't show until ready
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
  });

  // Load the app
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, 'build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus on window (in case it's hidden behind other windows)
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });

  return mainWindow;
}

/**
 * Creates the application menu
 */
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Test',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow?.webContents.send('menu-new-test');
          }
        },
        {
          label: 'Open Results',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            mainWindow?.webContents.send('menu-open-results');
          }
        },
        { type: 'separator' },
        {
          label: 'Export Results',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            mainWindow?.webContents.send('menu-export-results');
          }
        },
        { type: 'separator' },
        {
          label: 'Quit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About ModelBench Pro',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About ModelBench Pro',
              message: 'ModelBench Pro',
              detail: 'A comprehensive AI model evaluation platform\nVersion 1.0.0'
            });
          }
        }
      ]
    }
  ];

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    });

    // Window menu
    template[4].submenu = [
      { role: 'close' },
      { role: 'minimize' },
      { role: 'zoom' },
      { type: 'separator' },
      { role: 'front' }
    ];
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(async () => {
  try {
    // Initialize database first
    await initializeDatabase();
    
    // Then create the main window and menu
    createMainWindow();
    createMenu();
  } catch (error) {
    console.error('Failed to start application:', error);
    app.quit();
    return;
  }

  app.on('activate', async () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      // Ensure database is still initialized
      if (!database) {
        try {
          await initializeDatabase();
        } catch (error) {
          console.error('Failed to reinitialize database:', error);
          app.quit();
          return;
        }
      }
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // Clean up database connection
  cleanupDatabase();
  
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  // Ensure database is properly closed before quitting
  cleanupDatabase();
});

app.on('will-quit', () => {
  // Final cleanup
  cleanupDatabase();
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (navigationEvent, navigationUrl) => {
    navigationEvent.preventDefault();
    require('electron').shell.openExternal(navigationUrl);
  });
});

// IPC handlers for secure communication with renderer
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

// Handle app protocol for deep linking (future feature)
if (process.defaultApp) {
  if (process.argv.length >= 2) {
    app.setAsDefaultProtocolClient('modelbench-pro', process.execPath, [path.resolve(process.argv[1])]);
  }
} else {
  app.setAsDefaultProtocolClient('modelbench-pro');
}