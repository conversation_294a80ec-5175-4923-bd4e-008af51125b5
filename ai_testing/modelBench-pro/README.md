# ModelBench Pro

A comprehensive cross-platform application for evaluating and comparing AI model performance across diverse domains through standardized prompt testing.

## 🚀 Features

### Multi-Provider Support
- **OpenAI**: GPT-4, GPT-3.5, and other models
- **Anthropic**: Claude 3 (Opus, Sonnet, Haiku)
- **Google**: Gemini Pro and Flash models
- **OpenRouter**: Unified access to multiple providers
- **OLLAMA**: Local model support for offline testing

### Comprehensive Testing Framework
- **Prompt Library**: 200+ curated prompts across 8 categories
- **Batch Testing**: Run multiple prompts across selected models
- **Real-time Evaluation**: Live pass/fail scoring
- **Custom Scoring**: Define evaluation metrics per prompt
- **Performance Metrics**: Response time, token usage, cost tracking

### Advanced Analytics
- **Pass Rate Dashboard**: Visual performance summaries
- **Model Comparison**: Side-by-side outputs and analysis
- **Historical Tracking**: Performance trends over time
- **Export Capabilities**: CSV, JSON, PDF reports

### Security & Cybersecurity Focus
- **Penetration Testing Prompts**: Specialized security evaluation
- **Adversarial Testing**: Security-focused prompt categories
- **Secure API Key Management**: AES-256 encryption
- **Input Sanitization**: Protection against prompt injection

## 🏗️ Architecture

### Technology Stack
- **Frontend**: React 18 + TypeScript 5
- **Desktop**: Electron 28
- **Styling**: TailwindCSS 3
- **Database**: SQLite with better-sqlite3
- **Build**: Webpack 5 with hot reload
- **Testing**: Jest + React Testing Library

### Project Structure
```
modelBench-pro/
├── src/
│   ├── components/     # React components
│   │   ├── Layout/     # Header, Sidebar, MainContent
│   │   ├── ModelPicker/# Model selection components
│   │   ├── PromptLibrary/# Prompt management
│   │   ├── Results/    # Results display and analysis
│   │   ├── Settings/   # Configuration panels
│   │   └── UI/         # Reusable UI components
│   ├── services/       # Business logic & API clients
│   │   ├── apiClients/ # Provider-specific clients
│   │   ├── database/   # SQLite operations
│   │   ├── testing/    # Test execution engine
│   │   └── security/   # Encryption & key management
│   ├── utils/          # Helper functions
│   ├── types/          # TypeScript definitions
│   ├── hooks/          # Custom React hooks
│   ├── data/           # Static data & prompts
│   └── styles/         # Global CSS and themes
├── public/             # Static assets
├── build/              # Build output
└── dist/               # Distribution packages
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd modelBench-pro

# Install dependencies
npm install

# Start development server
npm run dev

# In another terminal, start Electron
npm start
```

### Available Scripts
```bash
npm run dev          # Start webpack dev server
npm start            # Start Electron app
npm run build        # Build for production
npm run build-mac    # Build macOS app
npm run build-win    # Build Windows app
npm run build-all    # Build for all platforms
npm test             # Run tests
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

## 📋 Development Phases

### Phase 1: MVP Foundation ✅
- [x] Project structure initialization
- [x] Basic UI framework with React + TypeScript
- [x] Electron desktop app setup
- [x] TailwindCSS styling system
- [x] Cross-platform build configuration

### Phase 2: Core Features (In Progress)
- [ ] Database layer with SQLite
- [ ] OpenAI API client implementation
- [ ] Basic prompt library (50 prompts)
- [ ] Model picker component
- [ ] Test execution engine
- [ ] Results display system

### Phase 3: Multi-Provider Support
- [ ] Anthropic Claude integration
- [ ] Google Gemini integration
- [ ] OpenRouter API client
- [ ] OLLAMA local model support
- [ ] Unified API abstraction layer

### Phase 4: Advanced Features
- [ ] Batch testing system
- [ ] Advanced analytics dashboard
- [ ] Custom evaluation criteria
- [ ] Export and reporting system
- [ ] Security hardening

## 🔒 Security Features

### API Key Management
- AES-256 encryption for stored credentials
- Secure key derivation functions
- Environment variable support
- Key validation and testing

### Prompt Security
- Input sanitization for custom prompts
- Protection against prompt injection
- Secure evaluation sandboxing
- Audit logging for security events

### Application Security
- Content Security Policy (CSP)
- Secure communication protocols
- Regular security audits
- Penetration testing integration

## 🎯 Target Platforms

### Primary Platforms
- **macOS**: M1/M2 Apple Silicon + Intel
- **Windows**: Windows 10/11 (x64)

### System Requirements
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 500MB available space
- **Network**: Internet connection for API access
- **Optional**: NVIDIA GPU for local model acceleration

## 📊 Performance Targets

- **Startup Time**: < 3 seconds
- **API Response**: < 2 seconds for single requests
- **Memory Usage**: < 500MB baseline
- **Bundle Size**: < 100MB for distribution

## 🤝 Contributing

### Development Standards
- TypeScript strict mode with proper typing
- React functional components with hooks
- Comprehensive error handling
- JSDoc comments for all functions
- Cross-platform compatibility testing

### Code Quality
- ESLint + Prettier for code formatting
- Jest for unit testing
- React Testing Library for component tests
- 70%+ test coverage requirement

### Security Guidelines
- Regular dependency updates
- Security-focused code reviews
- Vulnerability scanning
- Secure coding practices

## 📄 License

MIT License - see LICENSE.md for details

## 🔗 Links

- [Documentation](./docs/)
- [API Reference](./docs/api/)
- [Contributing Guide](./CONTRIBUTING.md)
- [Security Policy](./SECURITY.md)
- [Changelog](./CHANGELOG.md)

## 🆘 Support

For support, please:
1. Check the [documentation](./docs/)
2. Search [existing issues](../../issues)
3. Create a [new issue](../../issues/new) with detailed information

---

**ModelBench Pro** - AI Model Intelligence Evaluation Platform  
Built with ❤️ for the AI research and cybersecurity community