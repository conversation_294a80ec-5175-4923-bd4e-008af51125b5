{"name": "modelbench-pro", "version": "1.0.0", "description": "A comprehensive cross-platform application for evaluating and comparing AI model performance across diverse domains through standardized prompt testing", "main": "electron.js", "homepage": "./", "scripts": {"start": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "dev": "webpack serve --mode development", "build": "webpack --mode production", "build-electron": "npm run build && electron-builder", "build-mac": "npm run build && electron-builder --mac", "build-win": "npm run build && electron-builder --win", "build-all": "npm run build && electron-builder --mac --win", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "keywords": ["ai", "model-evaluation", "benchmarking", "openai", "anthropic", "gemini", "ollama", "electron", "react", "typescript"], "author": "ModelBench Pro Team", "license": "MIT", "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "concurrently": "^8.2.2", "css-loader": "^6.8.1", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "style-loader": "^3.3.3", "tailwindcss": "^3.3.6", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "wait-on": "^7.2.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "dependencies": {"@anthropic-ai/sdk": "^0.9.1", "@google/generative-ai": "^0.1.3", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "better-sqlite3": "^9.2.2", "clsx": "^2.0.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "electron-store": "^8.1.0", "lucide-react": "^0.294.0", "openai": "^4.20.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.30.1", "recharts": "^2.8.0"}, "build": {"appId": "com.modelbenchpro.app", "productName": "ModelBench Pro", "directories": {"output": "dist"}, "files": ["build/**/*", "electron.js", "package.json"], "mac": {"category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}