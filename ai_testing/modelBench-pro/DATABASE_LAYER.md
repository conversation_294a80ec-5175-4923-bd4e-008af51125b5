# ModelBench Pro - Database Layer Documentation

## Overview

The database layer for ModelBench Pro provides a robust, type-safe foundation for storing and managing AI model benchmarking data. Built with SQLite and better-sqlite3, it offers synchronous operations, transaction support, and comprehensive error handling.

## Architecture

### Core Components

1. **DatabaseService.ts** - Main database service class with CRUD operations
2. **DatabaseErrors.ts** - Custom error classes and error handling utilities
3. **database.ts** - TypeScript interfaces and type definitions
4. **schema.sql** - Database schema with tables, indexes, and triggers
5. **useDatabase.ts** - React hooks for database operations
6. **DatabaseUtils.ts** - Utility functions for backup, maintenance, and validation
7. **electron.ts** - TypeScript definitions for Electron API

### Database Schema

#### Tables

- **models** - AI model definitions and metadata
- **benchmarks** - Benchmark test configurations
- **test_runs** - Individual benchmark execution records
- **results** - Detailed metric results from test runs
- **configurations** - Application and benchmark settings
- **db_metadata** - Schema versioning and database metadata

#### Key Features

- Foreign key constraints for data integrity
- Automatic timestamp management with triggers
- Optimized indexes for query performance
- JSON storage for flexible metadata
- Status tracking for test runs

## Usage Examples

### Basic Database Operations

```typescript
import { useDatabase } from '../hooks/useDatabase';

function MyComponent() {
  const { createModel, getModels, isConnected } = useDatabase();
  
  const handleCreateModel = async () => {
    const result = await createModel({
      name: 'GPT-4',
      provider: 'OpenAI',
      type: 'language-model',
      parameters: {
        temperature: 0.7,
        max_tokens: 2048
      }
    });
    
    if (result.success) {
      console.log('Model created:', result.data);
    } else {
      console.error('Error:', result.error);
    }
  };
  
  return (
    <div>
      <p>Database: {isConnected ? 'Connected' : 'Disconnected'}</p>
      <button onClick={handleCreateModel}>Create Model</button>
    </div>
  );
}
```

### Using Specialized Hooks

```typescript
import { useModels } from '../hooks/useDatabase';

function ModelsList() {
  const { 
    models, 
    loading, 
    error, 
    pagination, 
    loadModels, 
    addModel 
  } = useModels();
  
  useEffect(() => {
    loadModels();
  }, [loadModels]);
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return (
    <div>
      {models.map(model => (
        <div key={model.id}>{model.name}</div>
      ))}
      <p>Page {pagination.page} of {pagination.totalPages}</p>
    </div>
  );
}
```

### Transaction Support

```typescript
import { useDatabase } from '../hooks/useDatabase';

function BulkOperations() {
  const { executeTransaction } = useDatabase();
  
  const handleBulkCreate = async () => {
    const operations = [
      { method: 'createModel', args: [modelData1] },
      { method: 'createModel', args: [modelData2] },
      { method: 'createBenchmark', args: [benchmarkData] }
    ];
    
    const result = await executeTransaction(operations);
    if (result.success) {
      console.log('All operations completed successfully');
    } else {
      console.error('Transaction failed:', result.error);
    }
  };
  
  return <button onClick={handleBulkCreate}>Bulk Create</button>;
}
```

## Database Service API

### Model Operations

- `createModel(input: CreateModelInput): Promise<DatabaseResult<Model>>`
- `getModels(query?: ModelQuery, page?: number, pageSize?: number): Promise<DatabaseResult<PaginatedResult<Model>>>`
- `getModelById(id: number): Promise<DatabaseResult<Model>>`
- `updateModel(input: UpdateModelInput): Promise<DatabaseResult<Model>>`
- `deleteModel(id: number): Promise<DatabaseResult<void>>`

### Benchmark Operations

- `createBenchmark(input: CreateBenchmarkInput): Promise<DatabaseResult<Benchmark>>`
- `getBenchmarks(query?: BenchmarkQuery, page?: number, pageSize?: number): Promise<DatabaseResult<PaginatedResult<Benchmark>>>`
- `getBenchmarkById(id: number): Promise<DatabaseResult<Benchmark>>`

### Test Run Operations

- `createTestRun(input: CreateTestRunInput): Promise<DatabaseResult<TestRun>>`
- `getTestRunById(id: number): Promise<DatabaseResult<TestRun>>`
- `updateTestRun(input: UpdateTestRunInput): Promise<DatabaseResult<TestRun>>`

### Result Operations

- `createResult(input: CreateResultInput): Promise<DatabaseResult<Result>>`
- `getResultById(id: number): Promise<DatabaseResult<Result>>`

### Configuration Operations

- `createConfiguration(input: CreateConfigurationInput): Promise<DatabaseResult<Configuration>>`
- `getConfigurationById(id: number): Promise<DatabaseResult<Configuration>>`
- `getDefaultConfiguration(): Promise<DatabaseResult<Configuration>>`

### Utility Operations

- `getStats(): Promise<DatabaseResult<DatabaseStats>>`
- `backup(backupPath: string): Promise<DatabaseResult<void>>`
- `transaction<T>(callback: TransactionCallback<T>): DatabaseResult<T>`

## Error Handling

The database layer includes comprehensive error handling with custom error classes:

- **DatabaseError** - Base error class
- **DatabaseConnectionError** - Connection-related errors
- **DatabaseInitializationError** - Initialization failures
- **DatabaseMigrationError** - Schema migration issues
- **DatabaseTransactionError** - Transaction failures
- **DatabaseValidationError** - Input validation errors
- **DatabaseNotFoundError** - Record not found errors
- **DatabaseConstraintError** - Constraint violations

### Error Handling Example

```typescript
try {
  const result = await createModel(modelData);
  if (!result.success) {
    // Handle specific error types
    if (result.error?.includes('UNIQUE constraint')) {
      showError('A model with this name already exists');
    } else {
      showError(`Database error: ${result.error}`);
    }
  }
} catch (error) {
  if (error instanceof DatabaseValidationError) {
    showError(`Validation error: ${error.message}`);
  } else {
    showError('Unexpected error occurred');
  }
}
```

## Database Utilities

### Backup and Restore

```typescript
import { DatabaseUtils } from '../services/DatabaseUtils';

// Create backup with user dialog
const backupResult = await DatabaseUtils.createBackup();

// Export to JSON
const exportResult = await DatabaseUtils.exportToJSON();
```

### Health Monitoring

```typescript
// Check database health
const healthResult = await DatabaseUtils.getDatabaseHealth();
if (healthResult.success) {
  console.log('Health:', healthResult.data.health);
  console.log('Issues:', healthResult.data.issues);
}

// Validate integrity
const integrityResult = await DatabaseUtils.validateIntegrity();
if (integrityResult.success && !integrityResult.data.isValid) {
  console.error('Integrity errors:', integrityResult.data.errors);
}
```

### Automatic Maintenance

```typescript
import { DatabaseMaintenance } from '../services/DatabaseUtils';

// Start automatic maintenance (runs every 24 hours)
DatabaseMaintenance.startMaintenance(24);

// Stop maintenance
DatabaseMaintenance.stopMaintenance();
```

## Security Features

1. **Prepared Statements** - All queries use prepared statements to prevent SQL injection
2. **Input Validation** - Comprehensive validation before database operations
3. **Context Isolation** - Secure IPC communication between main and renderer processes
4. **Error Sanitization** - Sensitive information filtered from error messages

## Performance Optimizations

1. **Indexes** - Strategic indexes on frequently queried columns
2. **WAL Mode** - Write-Ahead Logging for better concurrency
3. **Prepared Statements** - Cached prepared statements for common operations
4. **Connection Pooling** - Single connection with proper lifecycle management
5. **Pagination** - Built-in pagination for large result sets

## Database Configuration

The database is configured with optimal settings:

```sql
PRAGMA journal_mode = WAL;        -- Write-Ahead Logging
PRAGMA synchronous = NORMAL;      -- Balanced durability/performance
PRAGMA cache_size = 1000;         -- Memory cache size
PRAGMA temp_store = memory;       -- Temporary tables in memory
PRAGMA foreign_keys = ON;         -- Enforce foreign key constraints
```

## Migration Support

The database layer includes migration support for schema updates:

- Version tracking in `db_metadata` table
- Migration scripts can be added for future schema changes
- Automatic migration execution on startup
- Rollback support for failed migrations

## Integration Points

### Electron Main Process

The database is initialized in the main Electron process (`electron.js`):

- Database file stored in user data directory
- Automatic initialization on app startup
- Graceful cleanup on app exit
- IPC handlers for secure communication

### React Renderer Process

Access via hooks and TypeScript interfaces:

- Type-safe API through `electronAPI`
- React hooks for state management
- Automatic error handling and loading states
- Pagination and filtering support

## Future Enhancements

1. **Real-time Updates** - WebSocket-like updates for live data
2. **Advanced Querying** - Complex filtering and sorting options
3. **Data Compression** - Compress large result datasets
4. **Encryption** - Optional database encryption for sensitive data
5. **Replication** - Database replication for backup and sync
6. **Performance Monitoring** - Query performance tracking and optimization

## Troubleshooting

### Common Issues

1. **Database Locked** - Ensure proper connection cleanup
2. **Migration Failures** - Check schema compatibility
3. **Performance Issues** - Review indexes and query patterns
4. **Connection Errors** - Verify file permissions and disk space

### Debug Mode

Enable verbose logging in development:

```typescript
const database = getDatabaseInstance({
  path: dbPath,
  verbose: true,  // Enables SQL query logging
  timeout: 30000
});
```

### Health Checks

Regular health checks help identify issues early:

```typescript
// Run health check
const health = await DatabaseUtils.getDatabaseHealth();
if (health.data?.health === 'critical') {
  // Take corrective action
}