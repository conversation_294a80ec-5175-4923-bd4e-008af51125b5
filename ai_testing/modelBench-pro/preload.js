const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App utilities
  getVersion: () => ipcRenderer.invoke('app-version'),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),

  // Menu event listeners
  onMenuNewTest: (callback) => ipcRenderer.on('menu-new-test', callback),
  onMenuOpenResults: (callback) => ipcRenderer.on('menu-open-results', callback),
  onMenuExportResults: (callback) => ipcRenderer.on('menu-export-results', callback),

  // Database API - Models
  createModel: (input) => ipcRenderer.invoke('db-create-model', input),
  getModels: (query, page, pageSize) => ipcRenderer.invoke('db-get-models', query, page, pageSize),
  getModelById: (id) => ipcRenderer.invoke('db-get-model-by-id', id),
  updateModel: (input) => ipcRenderer.invoke('db-update-model', input),
  deleteModel: (id) => ipcRenderer.invoke('db-delete-model', id),

  // Database API - Benchmarks
  createBenchmark: (input) => ipcRenderer.invoke('db-create-benchmark', input),
  getBenchmarks: (query, page, pageSize) => ipcRenderer.invoke('db-get-benchmarks', query, page, pageSize),
  getBenchmarkById: (id) => ipcRenderer.invoke('db-get-benchmark-by-id', id),

  // Database API - Test Runs
  createTestRun: (input) => ipcRenderer.invoke('db-create-test-run', input),
  getTestRunById: (id) => ipcRenderer.invoke('db-get-test-run-by-id', id),
  updateTestRun: (input) => ipcRenderer.invoke('db-update-test-run', input),

  // Database API - Results
  createResult: (input) => ipcRenderer.invoke('db-create-result', input),
  getResultById: (id) => ipcRenderer.invoke('db-get-result-by-id', id),

  // Database API - Configurations
  createConfiguration: (input) => ipcRenderer.invoke('db-create-configuration', input),
  getConfigurationById: (id) => ipcRenderer.invoke('db-get-configuration-by-id', id),
  getDefaultConfiguration: () => ipcRenderer.invoke('db-get-default-configuration'),

  // Database API - Utilities
  getDatabaseStats: () => ipcRenderer.invoke('db-get-stats'),
  backupDatabase: (backupPath) => ipcRenderer.invoke('db-backup', backupPath),
  executeTransaction: (operations) => ipcRenderer.invoke('db-transaction', operations),

  // Remove all listeners for cleanup
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Type definitions for TypeScript (will be used by renderer)
window.electronAPI = {
  // App utilities
  getVersion: () => Promise.resolve(''),
  showSaveDialog: (options) => Promise.resolve({}),
  showOpenDialog: (options) => Promise.resolve({}),

  // Menu event listeners
  onMenuNewTest: (callback) => {},
  onMenuOpenResults: (callback) => {},
  onMenuExportResults: (callback) => {},

  // Database API - Models
  createModel: (input) => Promise.resolve({}),
  getModels: (query, page, pageSize) => Promise.resolve({}),
  getModelById: (id) => Promise.resolve({}),
  updateModel: (input) => Promise.resolve({}),
  deleteModel: (id) => Promise.resolve({}),

  // Database API - Benchmarks
  createBenchmark: (input) => Promise.resolve({}),
  getBenchmarks: (query, page, pageSize) => Promise.resolve({}),
  getBenchmarkById: (id) => Promise.resolve({}),

  // Database API - Test Runs
  createTestRun: (input) => Promise.resolve({}),
  getTestRunById: (id) => Promise.resolve({}),
  updateTestRun: (input) => Promise.resolve({}),

  // Database API - Results
  createResult: (input) => Promise.resolve({}),
  getResultById: (id) => Promise.resolve({}),

  // Database API - Configurations
  createConfiguration: (input) => Promise.resolve({}),
  getConfigurationById: (id) => Promise.resolve({}),
  getDefaultConfiguration: () => Promise.resolve({}),

  // Database API - Utilities
  getDatabaseStats: () => Promise.resolve({}),
  backupDatabase: (backupPath) => Promise.resolve({}),
  executeTransaction: (operations) => Promise.resolve({}),

  // Remove all listeners for cleanup
  removeAllListeners: (channel) => {}
};