---
description: 
globs: 
alwaysApply: true
---
## Product Requirements Document (PRD)

### 1. Product Overview
**Product Name:** ModelBench Pro  
**Vision:** A comprehensive cross-platform application for evaluating and comparing AI model performance across diverse domains through standardized prompt testing.

### 2. Core Features

#### 2.1 Model Management
- **Multi-Provider Support:** OpenAI, Anthropic, Google/Gemini, OpenRouter, OLLAMA
- **Dynamic Model Discovery:** Auto-detect available models from each provider
- **Local Model Support:** OLLAMA integration for offline testing
- **API Key Management:** Secure credential storage with encryption

#### 2.2 Prompt Library System
```
Categories:
├── General Knowledge
├── Coding & Programming
├── Mathematics & Logic
├── Creative Writing
├── Cybersecurity & Pentesting 🎯
├── Reasoning & Problem Solving
├── Language Understanding
├── Ethical Scenarios
└── Custom Categories
```

#### 2.3 Testing Engine
- **Batch Testing:** Run multiple prompts across selected models
- **Real-time Evaluation:** Live pass/fail scoring
- **Custom Scoring Criteria:** Define evaluation metrics per prompt
- **Response Comparison:** Side-by-side model outputs
- **Performance Metrics:** Response time, token usage, cost tracking

#### 2.4 Results & Analytics
- **Pass Rate Dashboard:** Visual performance summaries
- **Model Comparison Charts:** Radar charts, bar graphs
- **Export Capabilities:** CSV, JSON, PDF reports
- **Historical Tracking:** Performance trends over time

### 3. Technical Architecture

#### 3.1 Cross-Platform Framework
**Recommendation:** Electron + React/TypeScript
- Single codebase for Mac/Windows
- Native OS integration
- Rich UI capabilities
- Easy API integration

#### 3.2 Alternative Stack Options
```typescript
// Option 1: Tauri + React (Rust-based, lighter than Electron)
// Option 2: Flutter Desktop
// Option 3: .NET MAUI (if you're comfortable with C#)
```

#### 3.3 Backend Architecture
```
Local SQLite Database
├── Models Configuration
├── Prompt Library
├── Test Results
├── User Settings
└── API Credentials (encrypted)
```

### 4. UI/UX Design Specifications

#### 4.1 Layout Structure
```
┌─────────────────────────────────────────────────────┐
│ Header: Model Selector | Settings | Export          │
├─────────────────┬───────────────────────────────────┤
│ Prompt Library  │ Results Panel                     │
│ ├─ Categories   │ ├─ Model Comparison               │
│ ├─ Search       │ ├─ Pass/Fail Indicators           │
│ ├─ Filters      │ ├─ Response Preview               │
│ └─ Custom       │ └─ Performance Metrics            │
├─────────────────┼───────────────────────────────────┤
│ Test Controls   │ Live Results Feed                 │
└─────────────────┴───────────────────────────────────┘
```

#### 4.2 Key Components
- **Model Picker:** Dropdown with provider icons and model specs
- **Prompt Cards:** Expandable cards with difficulty indicators
- **Progress Indicators:** Real-time testing progress
- **Results Grid:** Sortable, filterable results table

### 5. Development Phases

#### Phase 1: MVP (4-6 weeks)
- Basic UI framework
- Single provider integration (OpenAI)
- Core prompt library (50 prompts)
- Simple pass/fail evaluation
- Local data storage

#### Phase 2: Multi-Provider (3-4 weeks)
- All API integrations
- OLLAMA local model support
- Enhanced prompt categories
- Batch testing capabilities

#### Phase 3: Analytics & Polish (3-4 weeks)
- Advanced analytics dashboard
- Export functionality
- Custom prompt creation
- Performance optimizations

### 6. Technical Implementation

#### 6.1 Sample Project Structure
```
modelBench-pro/
├── src/
│   ├── components/
│   │   ├── ModelPicker/
│   │   ├── PromptLibrary/
│   │   ├── ResultsPanel/
│   │   └── Dashboard/
│   ├── services/
│   │   ├── apiClients/
│   │   ├── database/
│   │   └── evaluation/
│   ├── utils/
│   └── types/
├── public/
├── build/
└── package.json
```

#### 6.2 Key Dependencies
```json
{
  "electron": "^latest",
  "react": "^18.x",
  "typescript": "^5.x",
  "sqlite3": "^5.x",
  "axios": "^1.x",
  "recharts": "^2.x",
  "tailwindcss": "^3.x"
}
```

### 7. Security Considerations
- Encrypted API key storage
- Secure local database
- Rate limiting for API calls
- Input sanitization for custom prompts
- Optional proxy support for enterprise environments

### 8. Monetization Strategy (Future)
- Freemium model with basic features
- Pro version with advanced analytics
- Enterprise features for team collaboration
- Custom prompt marketplace

